{"name": "woocommerce-order-barcodes", "title": "WooCommerce Order Barcodes", "version": "1.9.4", "homepage": "http://www.woocommerce.com/products/woocommerce-order-barcodes/", "repository": {"type": "git", "url": "git://github.com/woocommerce/woocommerce-order-barcodes.git"}, "config": {"use_pnpm": true, "translate": false, "use_gh_release_notes": true, "paths": {"js": "assets/js/*.js", "js_min": "assets/js/*.min.js", "css": "assets/css/*.css", "sass": "assets/css", "cssfolder": "assets/css", "js_dependencies_onscan": "node_modules/onscan.js/onscan.js"}}, "devDependencies": {"babel-minify": "^0.5.1", "clean-css-cli": "^4.3.0", "sass": "^1.77.5", "node-wp-i18n": "~1.2.3"}, "assets": {"js": {"min": "assets/js/*.min.js", "js": "assets/js/*.js"}, "styles": {"css": "assets/css/*.css", "sass": "assets/css/*.scss", "cssfolder": "assets/css/"}}, "js_dependencies": {"onscan": "node_modules/onscan.js/onscan.js"}, "scripts": {"prebuild": "rm -rf ./vendor && cp $npm_package_config_paths_js_dependencies_onscan assets/js/", "build": "composer install --no-dev && pnpm run minify && pnpm run makepot && pnpm run sass && pnpm run archive", "build:dev": "composer install && pnpm run minify && pnpm run makepot && pnpm run sass", "archive": "composer archive --file=$npm_package_name --format=zip", "postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name", "preminify": "rm -f $npm_package_config_paths_js_min", "minify": "for f in $npm_package_config_paths_js; do file=${f%.js}; node_modules/.bin/minify $f --out-file $file.min.js; done", "presass": "rm -f $npm_package_config_paths_css", "sass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --no-source-map --style compressed", "watchsass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --watch", "postsass": "for f in $npm_package_config_paths_css; do file=${f%.css}; node_modules/.bin/cleancss -o $file.css $f; done", "makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude node_modules,tests,docs"}, "engines": {"node": "^22.14.0", "pnpm": "^10.4.1"}, "dependencies": {"onscan.js": "^1.5.2"}}
[![CI](https://github.com/woocommerce/woocommerce-order-barcodes/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-order-barcodes/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-order-barcodes/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-order-barcodes/actions/workflows/cron_qit.yml)

woocommerce-order-barcodes
====================

Generates unique barcodes for your orders - perfect for e-tickets, packing slips, reservations and a variety of other uses.

## NPM Scripts

WooCommerce Order Barcodes utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files. This also copies a JS dependency file called onscan.js from the /node_modules/ folder to the /assets/ folder so that it can be minified and used by the plugin. 

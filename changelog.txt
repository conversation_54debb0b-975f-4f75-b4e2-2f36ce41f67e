*** WooCommerce Order Barcodes Changelog ***

2025-08-11 - version 1.9.4
* Tweak - WooCommerce 10.1 compatibility.

2025-07-07 - version 1.9.3
* Tweak - WooCommerce 10.0 compatibility.

2025-06-09 - version 1.9.2
* Tweak - WooCommerce 9.9 compatibility.

2025-06-03 - version 1.9.1
* Update Requires headers for WooCommerce compatibility.
* Update to ubuntu-latest to fix QIT tests.

2025-05-05 - version 1.9.0
* Add - 'woocommerce_order_barcode_parameters' filter to allow customization of barcode dimensions, colors, and padding.
* Fix - Barcode scanning issue in dark mode emails by ensuring background extends properly.

2025-04-07 - version 1.8.5
* Tweak - WooCommerce 9.8 compatibility.

2025-03-04 - version 1.8.4
* Tweak - WooCommerce 9.7 compatibility.

2025-01-27 - version 1.8.3
* Tweak - PHP 8.4 compatibility.

2025-01-07 - version 1.8.2
* Fix   - Restore High-Performance Order Storage (HPOS) and WooCommerce Blocks compatibility.

2024-10-29 - version 1.8.1
* Tweak - WordPress 6.7 Compatibility.

2024-09-30 - version 1.8.0
* Add - Background color settings for the barcode to make the barcode scannable in dark theme or page.

2024-07-02 - version 1.7.5
* Tweak - WordPress 6.6 & WooCommerce 9.0 Compatibility.

2024-03-25 - version 1.7.4
* Tweak - WordPress 6.5 compatibility.

2023-12-11 - version 1.7.3
* Add - Declared cart/checkout blocks compatibility.

2023-09-18 - version 1.7.2
* Tweak  - Applying WordPress Coding Standards.

2023-09-05 - version 1.7.1
* Update - Security updates.

2023-08-08 - version 1.7.0
* Add    - New barcode library for PHP 8 compatibility.

2023-06-12 - version 1.6.5
* Update - Security update.

2023-05-01 - version 1.6.4
* Update - Security update.

2023-02-02 - version 1.6.3
* Tweak - To make `display_barcode()` available for other plugins.

2023-01-23 - version 1.6.2
* Fix   - Automatic barcode generation no longer fails with "Barcode will be generated after Order is created!" message.

2023-01-19 - version 1.6.1
* Fix   - Display a notice text on Add New Order screen.

2022-11-03 - version 1.6.0
* Add   - Declared High-Performance Order Storage (HPOS) compatibility.

2022-10-13 - version 1.5.2
* Fix   - Add WCC Box Office compatibility for displaying a ticket barcode.

2022-09-22 - version 1.5.1
* Fix   - Prevent adding order meta box to other post types.
* Tweak - WC 6.9 compatibility.

2022-09-15 - version 1.5.0
* Tweak - Custom Order Tables (COT) compatibility.
* Fix   - Exclude unnecessary files from plugin zip file.

2022-08-15 - version 1.4.0
* Add   - Add bulk generate barcode tools for the order before the plugin being installed.
* Tweak - Transition version numbering to WordPress versioning.
* Tweak - WC 6.7.0 and WP 6.0.1 compatibility.

2022-06-09 - version 1.3.26
* Fix   - Plugin cannot read numerical barcode.

2022-01-05 - version 1.3.25
* Fix   - Missing Languages folder and .pot file in release-ready zip file.

2021-07-27 - version 1.3.24
* Tweak - Remove barcode field in checkout flow, instead create barcode once order created.
* Tweak - Add barcode URL in REST API order creation response.
* Tweak - Display barcode on the edit order page as PNG, instead of HTML. Fixes FireFox display issue.

2020-09-23 - version 1.3.23
* Fix   - Error loading onscan.js file on Barcode Scanner page.

2020-08-19 - version 1.3.22
* Tweak - WordPress 5.5 compatibility.

2020-07-22 - version 1.3.21
* Fix - Show barcode as image in emails and on frontend pages.
* Tweak - Remove email link for viewing barcode in browser.

2020-07-14 - version 1.3.20
* Fix - Add link to order in email.

2020-07-01 - version 1.3.19
* Add - Filter to hide barcode from display. `woocommerce_order_barcodes_display_barcode`
* Fix - Checkout page load performance due to large barcode images.
* Fix - Order barcode scan not displaying invalid notices.
* Tweak - Scan barcode form layout for mobile devices.

2020-06-10 - version 1.3.18
* Tweak - WC 4.2 compatibility.

2020-04-29 - version 1.3.17
* Tweak - WC 4.1 compatibility.

2020-03-04 - version 1.3.16
* Tweak - Remove legacy code.
* Tweak - WC 4.0 compatibility.

2020-01-21 - version 1.3.15
* Add - Extension constant version.

2020-01-13 - version 1.3.14
* Tweak - WC tested up to 3.9

2019-11-04 - version 1.3.13
* Tweak - WC tested up to 3.8

2019-10-22 - version 1.3.12
* Fix - Box Office ticets not displaying.

2019-10-10 - version 1.3.11
* Fix - Barcodes not displaying in order emails.

2019-09-03 - version 1.3.10
* Fix - Store managers could not view barcodes for orders generated by customers.

2019-08-28 - version 1.3.9
* Fix - Barcode visible at URL using order number.

2019-08-20 - version 1.3.8
* Fix - Unable to change barcode types.

2019-08-08 - version 1.3.7
* Tweak - WC tested up to 3.7

2019-04-16 - version 1.3.6
* Tweak - WC tested up to 3.6

2019-03-12 - version 1.3.5
* Fix - Barcode doesn't appear correctly in Gmail.

2018-10-09 - version 1.3.4
* Updated - WC tested up to 3.5

2018-05-23 - version 1.3.3
* Updated - WC tested up to 3.4
* Add - GDPR support

2017-12-13 - version 1.3.2
* Fix - WC 3.3 compatibility.

2017-09-07 - version 1.3.1
* New - Added filter `wc_order_barcodes_remove_image_from_api` to remove the order barcode image from API responses. Disabled by default, in case existing merchants are relying on that image.

2016-01-11 - version 1.3.0
* Tweak - Improved [scan_barcode] form check and notice messages.
* Fix - Invalid action should be warned and order info shouldn't be displayed.

2015-05-15 - version 1.2.1
* Tweak - Minifying CSS
* Fix - Removing Chosen drop down on scan form

2015-03-27 - version 1.2.0
* New - Adding wc_order_barcode() function for fetching a specific order's barcode
* Fix - Fixing PHP strict standards errors

2014-10-03 - version 1.1.2
* Tweak - Adding option to switch off barcode generation (on by default)
* Tweak - Adding 'woocommerce_order_barcodes_barcode_string' filter to allow the generated barode string to be modified on the fly

2014-09-08 - version 1.1.1
* Fix - Fixing CSS for admin barcode display
* Tweak - Updating queries for WooCommerce 2.2

2014-07-14 - version 1.1
* New - Added woocommerce_order_barcodes_do_nonce_check filter that will allow you to disable the nonce check on scanning
* New - Added woocommerce_order_barcodes_scan_permission filter that will allow you to modify the permissions needed to scan barcodes
* Tweak - Added order ID to woocommerce_order_barcodes_complete_order filter
* Tweak - Improved error messages when scanning fails
* Tweak - Improved a few inline comments

2014-06-03 - version 1.0.1
* Adding woocommerce_order_barcodes_complete_order filter to allow third parties to add custom conditions for order completion

2014-06-03 - version 1.0.0
* Initial release! Woo!

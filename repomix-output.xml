This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: verndor/, node_modules/, languages/, lib/
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
.github/
  ISSUE_TEMPLATE/
    config.yml
  workflows/
    build.yml
    cron_qit.yml
    manual_qit.yml
    merge_to_trunk.yml
    qit_runner.yml
    update-requires-headers.yml
  CONTRIBUTING.md
  ISSUE_TEMPLATE.md
  PULL_REQUEST_TEMPLATE.md
assets/
  css/
    admin.scss
    frontend.scss
  js/
    frontend.js
includes/
  class-woocommerce-order-barcodes-privacy.php
  class-woocommerce-order-barcodes-settings.php
  class-woocommerce-order-barcodes.php
  trait-woocommerce-order-util.php
  woocommerce-order-barcodes-functions.php
templates/
  barcode-image.php
  index.php
.gitignore
.nvmrc
.phpcs.security.xml
changelog.txt
composer.json
README.md
woocommerce-order-barcodes.php
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path=".github/CONTRIBUTING.md">
### Create Bug Reports

If you or customers find a bug, let us know by creating a new issue. You can [check our requirements to create good bug reports here](https://extendomattic.wordpress.com/triaging-issues/#issues-requirements).

### Write and submit a patch

If you'd like to fix a bug, you can submit a Pull Request. If possible, raises an issue first and link the issue in your [commit message](https://help.github.com/articles/closing-issues-via-commit-messages/) or [PR's body](https://github.com/blog/1506-closing-issues-via-pull-requests).

When creating Pull Requests, remember:

- [Check In Early, Check In Often](http://blog.codinghorror.com/check-in-early-check-in-often/).
- Write [good commit messages](http://tbaggery.com/2008/04/19/a-note-about-git-commit-messages.html).
- Respect the [Best practices for WordPress development](http://jetpack.com/contribute/#practices).
</file>

<file path="assets/css/frontend.scss">
.woocommerce-checkout {
	#order_barcode_image, #order_barcode_text {
		display: none;
	}
	#barcode_container {
		display: none;
	}
}
#barcode-scan-form {
	text-align: center;
	select {
		text-align: left;
	}
	input#scan-code {
		margin: 0 10px;
	}
}
#barcode-scan-loader {
	text-align: center;
	line-height: 70px;
	font-style: italic;
	display: none;
}
#barcode-scan-result {
	margin-top: 20px;
	h3.checked_in {
		text-align: center;
		&.yes {
			color: green;
		}
		&.no {
			color: red;
		}
	}
}
#view-order-barcode {
	width: 100%;
	text-align: center;
	img {
		border: 0;
		padding: 0;
		margin: 0;
		box-shadow: none;
		border-radius: 0;
	}

	span {
		font-size: 110%;
	}
}
@media only screen and (max-width: 500px) {
	#barcode-scan-form {
		select#scan-action {
			width: 100%;
		}

		input#scan-code {
			display: block;
			margin: 10px 0;
			width: 100%;
		}
	}
}
</file>

<file path="assets/js/frontend.js">
const OrderBarcode = function() {
	const form          = document.querySelector( '#barcode-scan-form form' );
	const loader        = document.getElementById( 'barcode-scan-loader' );
	const input         = document.querySelector( '#barcode-scan-form input#scan-code' );
	const displayResult = document.getElementById( 'barcode-scan-result' );

	// Focus on barcode input field.
	input.focus();

	form.addEventListener( 'submit', ( event ) => {
		event.preventDefault();

		// Show the loader.
		loader.style.display = 'block';

		// Empty existing results.
		displayResult.innerHTML = '';

		const inputAction = document.querySelector( '#barcode-scan-form #scan-action' ).value;
		const request     = new XMLHttpRequest();

		request.open( 'POST', wc_order_barcodes.ajaxurl, true );
		request.setRequestHeader( 'Content-Type', 'application/x-www-form-urlencoded; charset=UTF-8' );
		request.setRequestHeader( 'X-Requested-With', 'XMLHttpRequest' );
		request.onreadystatechange = function() {
			if ( this.readyState === XMLHttpRequest.DONE && 200 === this.status ) {
				// Focus on barcode input field.
				input.focus();

				if ( ! request.response ) {
					return;
				}

				// Hide the loader.
				loader.style.display = 'none';

				// Display response.
				displayResult.innerHTML = request.response;
			}

			return;
		}

		request.send( encodeURI( 'action=scan_barcode&barcode_input=' + input.value + '&scan_action=' + inputAction + '&woocommerce_order_barcodes_scan_nonce=' + wc_order_barcodes.scan_nonce ) );
	} );

	onScan.attachTo( document, {
		reactToPaste: false,
		onScan: function( sCode, iQty ) {
			form.dispatchEvent( new Event( 'submit' ) );
		}
	} );
};

window.onload = OrderBarcode;
</file>

<file path=".github/workflows/cron_qit.yml">
name: Cron QIT

on:
  schedule:
    # Run at 02:00 on Sundays.
    - cron: '0 2 * * 0'

jobs:

  build:
    name: Build project
    uses: ./.github/workflows/build.yml
    with:
      plugin_name: woocommerce-order-barcodes

  qit-tests:
    name: QIT
    needs: build
    uses: ./.github/workflows/qit_runner.yml
    secrets: inherit
    with:
      extension: ${{ needs.build.outputs.plugin_name }}
      artifact: ${{ needs.build.outputs.plugin_name }}
      wp-version: 'stable'
      wc-version: 'nightly'
      test-activation: true
      test-security: true
      test-phpcompatibility: true
      test-validation: true
      test-phpstan: false # TODO: Enable this once we've fixed the PHPStan issues.
      test-woo-api: true
      test-woo-e2e: true
      test-malware: true
      test-plugin-check: false # only run for WPORG

  handle-success:
    if: ${{ success() }}
    needs: qit-tests
    name: Handle success
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: success
          message: 'Scheduled workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> passed.'

  handle-error:
    if: ${{ failure() }}
    needs: qit-tests
    name: Handle failure
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: failure
          message: 'Scheduled workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> failed. You can find the results in the artifacts section.'
</file>

<file path=".github/workflows/manual_qit.yml">
name: Manual Test Runner

on:
  workflow_dispatch:
    inputs:
      wp-version:
        description: 'WordPress Version to be tested: `latest` or a specific version number.'
        type: string
        default: 'latest'
      wc-version:
        description: 'WooCommerce Version to be tested: `latest`, `nightly` or a specific version number.'
        type: string
        default: 'latest'
      php-version:
        description: |
          PHP version. Default is `8.4`.
        type: string
        default: '8.4'
      extension-tests:
        description: 'Extension Tests'
        type: choice
        default: 'All'
        options:
          #   - 'E2E Tests' // enable this once we have E2E tests
          - 'Unit Tests'
          - 'All'
          - 'None'
      qit-tests:
        description: 'QIT Tests'
        type: choice
        options:
          - 'WooCommerce Pre-Release Tests (includes Activation, WooCommerce E2E and API tests)'
          - 'Code Quality Checks (includes Security, Validation, Malware, PHPStan, and PHP Compatibility tests)'
          - 'Activation'
          - 'Security'
          - 'Validation'
          - 'Malware'
          - 'PHPStan'
          - 'PHP Compatibility'
          - 'WooCommerce E2E and API tests'
          - 'Plugin Checks (for dotOrg metadata)'
          - 'None'
      qit-wait:
        description: 'Wait for QIT? Requires additional time for the QIT tests to complete within GHA.'
        type: boolean
        default: true
      options:
        description: 'QIT Additional options for `qit` command, like `--optional_features=hpos`.'
        type: string
        default: ''

run-name: Tests with WP-${{ inputs.wp-version }} - WC-${{ inputs.wc-version }} - PHP ${{ inputs.php-version }} - Tests ${{ inputs.extension-tests }} - QIT ${{ inputs.qit-tests }}

jobs:
  build_project:
    name: Package
    uses: ./.github/workflows/build.yml
    with:
      plugin_name: woocommerce-order-barcodes

  qit-tests:
    name: QIT
    needs: build_project
    uses: ./.github/workflows/qit_runner.yml
    with:
      extension: ${{ needs.build_project.outputs.plugin_name }}
      artifact: ${{ needs.build_project.outputs.plugin_name }}
      wp-version: ${{ inputs.wp-version == 'latest' && 'stable' || inputs.wp-version }}
      wc-version: ${{ inputs.wc-version == 'latest' && 'stable' || contains( inputs.wc-version, 'rc' ) && 'rc' || inputs.wc-version }}
      php-version: ${{ inputs.php-version }}
      test-activation: ${{ contains(inputs.qit-tests, 'Activation') && true || false }}
      test-security: ${{ contains(inputs.qit-tests, 'Security') && true || false }}
      test-phpcompatibility: ${{ contains(inputs.qit-tests, 'Compatibility') && true || false }}
      test-phpstan: ${{ contains(inputs.qit-tests, 'PHPStan') && true || false }}
      test-woo-api: ${{ contains(inputs.qit-tests, 'API') && true || false }}
      test-woo-e2e: ${{ contains(inputs.qit-tests, 'WooCommerce E2E') && true || false }}
      test-malware: ${{ contains(inputs.qit-tests, 'Malware') && true || false }}
      test-validation: ${{ contains(inputs.qit-tests, 'Validation') && true || false }}
      test-plugin-check: ${{ contains(inputs.qit-tests, 'dotOrg') && true || false }}
      options: ${{ inputs.options }}
      wait: ${{ inputs.qit-wait }}
    secrets: inherit

  # e2e-tests: // enable this once we have E2E tests
  #     if: contains(inputs.extension-tests, 'All') || contains(inputs.extension-tests, 'E2E')
  #     name: E2E tests
  #     needs: build_project
  #     uses: ./.github/workflows/e2e_runner.yml
  #     with:
  #       wp_version: '[ "${{ inputs.wp-version }}" ]'
  #       wc_version: '[ "${{ inputs.wc-version }}" ]'
  #       php_version: '[ "${{ inputs.php-version }}" ]'
  #       test_mode: '[ "legacy", "blocks" ]'
  #     secrets: inherit

  handle-success:
    if: |
      always() &&
      (needs.qit-tests.result == 'success' || needs.qit-tests.result == 'skipped')
    needs: qit-tests
    name: Handle success
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: success
          message: 'Test Runner Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> passed.'

  handle-cancelled:
    if: cancelled()
    needs: qit-tests
    name: Handle cancellation
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: cancelled
          message: 'Test Runner Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> cancelled.'

  handle-error:
    if: failure()
    needs: qit-tests
    name: Handle failure
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: failure
          message: 'Test Runner Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> failed.'
</file>

<file path=".github/workflows/merge_to_trunk.yml">
name: Merge to Trunk CI

on:
  pull_request:
    branches:
      - trunk
    types:
      - closed

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  build:
    name: Build project
    uses: ./.github/workflows/build.yml
    with:
      plugin_name: woocommerce-order-barcodes

  qit-tests:
    if: github.event.pull_request.merged == true
    name: QIT tests
    needs: build
    uses: ./.github/workflows/qit_runner.yml
    secrets: inherit
    with:
      extension: ${{ needs.build.outputs.plugin_name }}
      artifact: ${{ needs.build.outputs.plugin_name }}
      test-activation: true
      test-security: true
      test-phpcompatibility: true
      test-validation: true
      test-phpstan: false # TODO: Enable this once the PHPStan tests are fixed

  handle-success:
    if: ${{ success() }}
    needs: qit-tests
    name: Handle success
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: success
          message: "Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> in PR <{{refUrl}}|{{ref}}> passed."

  handle-cancelled:
    if: ${{ cancelled() }}
    needs: qit-tests
    name: Handle cancellation
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: cancelled
          message: "Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> in PR <{{refUrl}}|{{ref}}> cancelled."

  handle-error:
    if: ${{ failure() }}
    needs: qit-tests
    name: Handle failure
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: failure
          message: "Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> in PR <{{refUrl}}|{{ref}}> failed."
</file>

<file path=".github/workflows/qit_runner.yml">
name: Run QIT

on:
    workflow_call:
        inputs:
            extension:
                required: true
                type: string
            artifact:
                type: string
                required: true
            wp-version:
                type: string
                default: 'stable'
            wc-version:
                type: string
                default: 'stable'
            php-version:
                type: string
                default: '8.4'
            # Customize which QIT test types to run.
            test-activation:
                type: string
                default: 'false'
            test-security:
                type: string
                default: 'false'
            test-phpcompatibility:
                type: string
                default: 'false'
            test-phpstan:
                type: string
                default: 'false'
            test-woo-api:
                type: string
                default: 'false'
            test-woo-e2e:
                type: string
                default: 'false'
            test-e2e:
                type: string
                default: 'false'
            test-additional-plugins:
                type: string
                default: ''
            test-malware:
                type: string
                default: 'false'
            test-plugin-check:
                type: string
                default: 'false'
            test-validation:
                type: string
                default: 'false'
            options:
                type: string
                default: ''
            # End of QIT tests.
            wait:
                type: boolean
                default: true

env:
    NO_COLOR: 1
    QIT_DISABLE_ONBOARDING: yes
    EXTENSION_SLUG: ${{ inputs.extension }}
    WP_VERSION: ${{ inputs.wp-version && inputs.wp-version || 'stable' }}
    WC_VERSION: ${{ inputs.wc-version && inputs.wc-version || 'stable' }}
    PHP_VERSION: ${{ inputs.php-version && inputs.php-version || '8.4' }}
    ADDITIONAL_PLUGINS: ${{ inputs.test-additional-plugins && format( '--additional_plugins={0}', inputs.test-additional-plugins ) || '' }}
    WAIT_FLAG: ${{ inputs.wait && '--wait' || '' }}
    EXTRA_OPTIONS: ${{ inputs.options }}

jobs:
    qit-tests:
        name: Run QIT Tests
        runs-on: ubuntu-latest
        steps:
            - name: Install QIT via composer
              shell: bash
              run: composer require woocommerce/qit-cli

            - name: Add Partner and Prepare Environment
              shell: bash
              run: |

                  # Add Partner.
                  ./vendor/bin/qit partner:add \
                    --user='${{ secrets.PARTNER_USER }}' \
                    --qit_token='${{ secrets.PARTNER_SECRET }}'

                  # Export ZIP file path to GITHUB_ENV.
                  echo "ZIP_FILE_PATH=${{ github.workspace }}/$EXTENSION_SLUG.zip" >> $GITHUB_ENV

                  # Create a directory to store QIT Results.
                  mkdir ${{ github.workspace }}/qit-results

            - name: Download the zip
              uses: actions/download-artifact@v4
              with:
                  name: ${{ inputs.artifact }}
                  path: ${{ github.workspace }}

            ##############################
            # Activation Tests.
            ##############################

            - name: Activation test
              id: run-activation-test
              if: ${{ inputs.test-activation == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  echo "Running activation test for ${{ env.EXTENSION_SLUG }}"
                  ./vendor/bin/qit run:activation \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --wp=${{ env.WP_VERSION }} \
                    --woo=${{ env.WC_VERSION }} \
                    --php_version=${{ env.PHP_VERSION }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-activation-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo activation results
              if: ${{ inputs.test-activation == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-activation-results.txt
                  exit ${{ steps.run-activation-test.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # Security Tests.
            ##############################

            - name: Run security test
              id: run-security-test
              if: ${{ inputs.test-security == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:security \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-security-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo security results
              if: ${{ inputs.test-security == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-security-results.txt
                  exit ${{ steps.run-security-test.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # PHP Compatibility Tests.
            ##############################

            - name: Run PHP compatibility test
              id: run-php-compatibility-test
              if: ${{ inputs.test-phpcompatibility == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:phpcompatibility \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-php-compatibility-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo PHP compatibility results
              if: ${{ inputs.test-phpcompatibility == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-php-compatibility-results.txt
                  exit ${{ steps.run-php-compatibility-test.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # PHPStan Tests.
            ##############################
            - name: Run PHPStan Tests
              id: run-phpstan-tests
              if: ${{ inputs.test-phpstan == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:phpstan \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --wordpress_version=${{ env.WP_VERSION == 'nightly' && 'rc' || env.WP_VERSION }}  \
                    --woocommerce_version=${{ env.WC_VERSION == 'nightly' && 'rc' || env.WC_VERSION }}  \
                    ${{ env.ADDITIONAL_PLUGINS }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-phpstan-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo PHPStan results
              if: ${{ inputs.test-phpstan == 'true' }}
              run: |

                  cat ${{ github.workspace }}/qit-results/qit-phpstan-results.txt
                  exit ${{ steps.run-phpstan-tests.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # WC API Tests.
            ##############################
            - name: Run WC API Tests
              id: run-woo-api-tests
              if: ${{ inputs.test-woo-api == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:woo-api \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --wordpress_version=${{ env.WP_VERSION == 'nightly' && 'rc' || env.WP_VERSION }} \
                    --woocommerce_version=${{ env.WC_VERSION == 'nightly' && 'rc' || env.WC_VERSION }} \
                    --php_version=${{ env.PHP_VERSION }} \
                    ${{ env.ADDITIONAL_PLUGINS }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-woo-api-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo WC API results
              if: ${{ inputs.test-woo-api == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-woo-api-results.txt
                  exit ${{ steps.run-woo-api-tests.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # Malware Tests.
            ##############################
            - name: Run Malware Tests
              id: run-malware-tests
              if: ${{ inputs.test-malware == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:malware \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-malware-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo Malware results
              if: ${{ inputs.test-malware == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-malware-results.txt
                  exit ${{ steps.run-malware-tests.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # Validation Tests.
            ##############################
            - name: Run Validation Tests
              id: run-validation-tests
              if: ${{ inputs.test-validation == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:validation \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --php_version=${{ env.PHP_VERSION }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-validation-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Upload results
              if: always()
              uses: actions/upload-artifact@v4
              with:
                  name: qit-results
                  path: ${{ github.workspace }}/qit-results/

            ##############################
            # Plugin Check Tests (for dotOrg).
            ##############################
            - name: Run Plugin Check Tests
              id: run-plugin-check-tests
              if: ${{ inputs.test-plugin-check == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:plugin-check \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-plugin-check-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo Plugin Check results
              if: ${{ inputs.test-plugin-check == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-plugin-check-results.txt
                  exit ${{ steps.run-plugin-check-tests.outputs.exitcode == 1 && 1 || 0 }}
                                    
            ##############################
            # WC E2E Tests.
            ##############################
            - name: Run WC E2E Tests
              id: run-woo-e2e-tests
              if: ${{ inputs.test-woo-e2e == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:woo-e2e \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --wordpress_version=${{ env.WP_VERSION == 'nightly' && 'rc' || env.WP_VERSION }} \
                    --woocommerce_version=${{ env.WC_VERSION == 'nightly' && 'rc' || env.WC_VERSION }} \
                    --php_version=${{ env.PHP_VERSION }} \
                    ${{ env.ADDITIONAL_PLUGINS }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-woo-e2e-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo WC E2E results
              if: ${{ inputs.test-woo-e2e == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-woo-e2e-results.txt
                  exit ${{ steps.run-woo-e2e-tests.outputs.exitcode == 1 && 1 || 0 }}
</file>

<file path=".github/ISSUE_TEMPLATE.md">
Before submitting a bug report be sure you have checked out [our support page](https://extendomattic.wordpress.com/extendables-support/) for information on labeling and other support channels.

<!-- You MUST add labels or this issue will be ignored. Please add a type (bug/enhancement/technical debt) and a priority (high/low). If these are not added, the issue will not be responded to or addressed. -->

**Describe the bug**
A clear and concise description of what the bug is. Please be as descriptive as possible.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Isolating the problem (mark completed items with an [x]):**
- [ ] I have deactivated other plugins and confirmed this bug occurs when only the extension is active.
- [ ] I can reproduce this bug consistently using the steps above.

**WordPress Environment**
<details>
```
Copy and paste the system status report from **WooCommerce > System Status** in WordPress admin.
```
</details>
</file>

<file path=".github/PULL_REQUEST_TEMPLATE.md">
### All Submissions:

* [ ] Does your code follow [WooCommerce](https://docs.woocommerce.com/document/create-a-plugin/) and [WordPress](https://make.wordpress.org/core/handbook/best-practices/coding-standards/) standards?
* [ ] Have you written new tests for your changes, as applicable?
* [ ] Have you successfully run tests with your changes locally?

<!-- Mark completed items with an [x] -->

<!-- You can erase any parts of this template not applicable to your Pull Request. -->

### Changes proposed in this Pull Request:

<!-- Describe the changes made to this Pull Request and the reason for such changes. -->

Closes # .

### How to test the changes in this Pull Request:

1.
2.
3.

### Other information:

* [ ] Have you checked to ensure there aren't other open [Pull Requests](../../pulls) for the same update/change?

<!-- Mark completed items with an [x] -->

### Changelog entry

> Enter a summary of all changes on this Pull Request. This will appear in the changelog if accepted.
</file>

<file path="assets/css/admin.scss">
.wc_order_barcodes_fields {
	.color_box {
		width: 100px !important;

		.woocommerce-help-tip {
			display:none;
		}
	}
}
#woocommerce-order-barcode {
	p {
		text-align: center;
		a {
			text-decoration: none;
		}
		img {
			width: 100%;
			height: auto;
		}
		span {
			font-size: 110%;
		}
	}
}
</file>

<file path="includes/class-woocommerce-order-barcodes-privacy.php">
<?php
/**
 * Privacy class for WC Order Barcodes plugin.
 *
 * @package woocommerce-order-barcodes
 */

if ( ! class_exists( 'WC_Abstract_Privacy' ) ) {
	return;
}

/**
 * Class WooCommerce_Order_Barcodes_Privacy
 *
 * A class to handle a privacy message.
 */
class WooCommerce_Order_Barcodes_Privacy extends WC_Abstract_Privacy {
	/**
	 * Constructor
	 */
	public function __construct() {
		parent::__construct( __( 'Order Barcodes', 'woocommerce-order-barcodes' ) );
	}

	/**
	 * Gets the message of the privacy to display.
	 */
	public function get_privacy_message() {
		// translators: %s link to plugin documentation.
		return wpautop( sprintf( __( 'By using this extension, you may be storing personal data or sharing data with an external service. <a href="%s" target="_blank">Learn more about how this works, including what you may want to include in your privacy policy.</a>', 'woocommerce-order-barcodes' ), 'https://docs.woocommerce.com/document/marketplace-privacy/#woocommerce-order-barcodes' ) );
	}
}

new WooCommerce_Order_Barcodes_Privacy();
</file>

<file path=".phpcs.security.xml">
<?xml version="1.0"?>
<ruleset name="Security sniffs from WordPress Coding Standards">
  <description>Security sniffs from WordPress Coding Standards</description>

  <arg value="sp"/>
  <arg name="colors"/>
  <arg name="extensions" value="php"/>
  <arg name="parallel" value="8"/>

  <config name="testVersion" value="7.2-"/>

  <!-- Do not fail PHPCS CI over warnings -->
  <config name="ignore_warnings_on_exit" value="1"/>

  <rule ref="WordPress.Security.EscapeOutput"/>
  <rule ref="WordPress.Security.EscapeOutput">
    <properties>
      <property name="customEscapingFunctions" type="array" value="wc_help_tip,wc_sanitize_tooltip,wc_selected,wc_kses_notice,wc_esc_json,wc_query_string_form_fields,wc_make_phone_clickable" />
    </properties>
  </rule>
  <rule ref="WordPress.Security.ValidatedSanitizedInput.InputNotSanitized"/>
  <rule ref="WordPress.Security.ValidatedSanitizedInput">
    <properties>
      <property name="customSanitizingFunctions" type="array" value="wc_clean,wc_sanitize_tooltip,wc_format_decimal,wc_stock_amount,wc_sanitize_permalink,wc_sanitize_textarea" />
    </properties>
  </rule>
  <!-- Encourage use of wp_safe_redirect() to avoid open redirect vulnerabilities.
     https://github.com/WordPress/WordPress-Coding-Standards/pull/1264 -->
  <rule ref="WordPress.Security.SafeRedirect"/>

  <!-- Verify that a nonce check is done before using values in superglobals.
     https://github.com/WordPress/WordPress-Coding-Standards/issues/73 -->
  <rule ref="WordPress.Security.NonceVerification"/>

  <!-- https://github.com/WordPress/WordPress-Coding-Standards/issues/1157 -->
  <rule ref="WordPress.Security.PluginMenuSlug"/>

  <!-- Covers rule: The eval() construct is very dangerous, and is impossible to secure. ... these must not be used. -->
  <rule ref="Squiz.PHP.Eval.Discouraged">
    <type>error</type>
    <message>eval() is a security risk so not allowed.</message>
  </rule>

</ruleset>
</file>

<file path=".github/ISSUE_TEMPLATE/config.yml">
blank_issues_enabled: false
contact_links:
  - name: We have moved to Linear!
    url: http://linear.app/team/WOOORBR/new
    about: Please open new issues on Linear.
</file>

<file path="includes/woocommerce-order-barcodes-functions.php">
<?php
/**
 * WC Order Barcodes function file.
 *
 * @package woocommerce-order-barcodes
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! function_exists( 'wc_order_barcode' ) ) {
	/**
	 * Fetch a barcode for a given order
	 *
	 * @param int    $order_id Order ID.
	 * @param string $before   Content to display before the barcode.
	 * @param string $after    Content to display after the barcode.
	 *
	 * @return string            Order barcode
	 */
	function wc_order_barcode( $order_id = 0, $before = '', $after = '' ) {
		return $before . WC_Order_Barcodes()->display_barcode( $order_id, false ) . $after;
	}
}

/**
 * Returns the main instance of WooCommerce_Order_Barcodes to prevent the need to use globals.
 *
 * @since  1.0.0
 * @return object WooCommerce_Order_Barcodes instance
 */
function WC_Order_Barcodes() { // phpcs:ignore
	$instance = WooCommerce_Order_Barcodes::instance();
	if ( is_null( $instance->settings ) ) {
		$instance->settings = WooCommerce_Order_Barcodes_Settings::instance( $instance );
	}
	return $instance;
}
</file>

<file path="templates/index.php">
<?php
/**
 * Silence is golder.
 *
 * @package woocommerce-order-barcodes
 */
</file>

<file path=".gitignore">
/nbproject/private/
node_modules
project.xml
project.properties
.DS_Store
Thumbs.db
.buildpath
.project
.settings*
.vscode
sftp-config.json
/deploy/
/wc-apidocs/
/languages/
screenshots/
/assets/**/*.min.js
/assets/**/onscan.js
/assets/**/*.css

# Ignore all log files except for .htaccess
/logs/*
!/logs/.htaccess

tests/e2e/config/local-*
.eslintcache

/vendor/

dist
.idea

woocommerce-order-barcodes.zip
</file>

<file path=".nvmrc">
22.14.0
</file>

<file path="includes/trait-woocommerce-order-util.php">
<?php
/**
 * Class WooCommerce\OrderBarcodes\Order_Util file.
 *
 * @package WooCommerce\OrderBarcodes
 */

namespace WooCommerce\OrderBarcodes;

use Automattic\WooCommerce\Utilities\OrderUtil;

defined( 'ABSPATH' ) || exit;

/**
 * Trait Order_Util
 *
 * A proxy-style trait that will help keep our code more stable and cleaner during the
 * transition to WC Custom Order Tables.
 */
trait Order_Util {
	/**
	 * Constant variable for OrderUtil class.
	 *
	 * @var wc_order_util_class.
	 */
	public static $wc_order_util_class = 'Automattic\WooCommerce\Utilities\OrderUtil';

	/**
	 * Constant variable for admin screen name.
	 *
	 * @var legacy_order_admin_screen.
	 */
	public static $legacy_order_admin_screen = 'shop_order';

	/**
	 * Checks whether the OrderUtil class exists
	 *
	 * @return bool
	 */
	public function wc_order_util_class_exists() {
		return class_exists( self::$wc_order_util_class );
	}

	/**
	 * Checks whether the OrderUtil class and the given method exist
	 *
	 * @param String $method_name Class method name.
	 *
	 * @return bool
	 */
	public function wc_order_util_method_exists( $method_name ) {
		if ( ! $this->wc_order_util_class_exists() ) {
			return false;
		}

		if ( ! method_exists( self::$wc_order_util_class, $method_name ) ) {
			return false;
		}

		return true;
	}

	/**
	 * Checks whether we are using custom order tables.
	 *
	 * @return bool
	 */
	public function custom_orders_table_usage_is_enabled() {
		if ( ! $this->wc_order_util_method_exists( 'custom_orders_table_usage_is_enabled' ) ) {
			return false;
		}

		return OrderUtil::custom_orders_table_usage_is_enabled();
	}

	/**
	 * Returns the relevant order screen depending on whether
	 * custom order tables are being used.
	 *
	 * @return string
	 */
	public function get_order_admin_screen() {
		if ( ! $this->wc_order_util_method_exists( 'get_order_admin_screen' ) ) {
			return self::$legacy_order_admin_screen;
		}

		return OrderUtil::get_order_admin_screen();
	}

	/**
	 * Check if the object is WP_Post object.
	 *
	 * @param Mixed $post_or_order Either Post object or Order object.
	 *
	 * @return Boolean
	 */
	public function is_wp_post( $post_or_order ) {
		return is_a( $post_or_order, 'WP_Post' );
	}

	/**
	 * Check if the object is WC_Order object.
	 *
	 * @param Mixed $post_or_order Either Post object or Order object.
	 *
	 * @return Boolean
	 */
	public function is_wc_order( $post_or_order ) {
		return is_a( $post_or_order, 'WC_Order' );
	}

	/**
	 * Check if the object is either WP_Post or WC_Order object.
	 *
	 * @param Mixed $post_or_order Either Post object or Order object.
	 *
	 * @return Boolean
	 */
	public function is_order_or_post( $post_or_order ) {
		return $this->is_wp_post( $post_or_order ) || $this->is_wc_order( $post_or_order );
	}

	/**
	 * Returns the WC_Order object from the object passed to
	 * the add_meta_box callback function.
	 *
	 * @param WC_Order|WP_Post $post_or_order_object Either Post object or Order object.
	 *
	 * @return \WC_Order
	 */
	public function init_theorder_object( $post_or_order_object ) {
		if ( ! $this->wc_order_util_method_exists( 'init_theorder_object' ) ) {
			return wc_get_order( $post_or_order_object->ID );
		}

		return OrderUtil::init_theorder_object( $post_or_order_object );
	}

	/**
	 * Get order status with 'wc-' prefix from order object.
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @return Boolean|String.
	 */
	public function get_order_status( $order ) {
		if ( ! method_exists( $order, 'get_status' ) ) {
			return false;
		}

		return ( false === strpos( $order->get_status(), 'wc-' ) ) ? 'wc-' . $order->get_status() : $order->get_status();
	}

	/**
	 * Return error text for unready barcode.
	 *
	 * @return String.
	 */
	public function barcode_not_ready_error_text() {
		return esc_html__( 'Barcode will be generated after Order is created!', 'woocommerce-order-barcodes' );
	}
}
</file>

<file path="templates/barcode-image.php">
<?php
/**
 * Barcode image template.
 *
 * @package woocommerce-order-barcodes
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Variables passed here from WooCommerce_Order_Barcodes::display_barcode():
 *
 * @var string $barcode_text
 * @var string $barcode_url
 * @var string $foreground_color
 */

?>

<img
	src="<?php echo esc_url( $barcode_url ); ?>"
	title="<?php echo esc_attr__( 'Barcode', 'woocommerce-order-barcodes' ); ?>"
	alt="<?php echo esc_attr__( 'Barcode', 'woocommerce-order-barcodes' ); ?>"
	style="display:inline;border:0;max-width:100%;margin: 0;"
/>
<br/>
<span
	style="<?php echo esc_attr( 'color: ' . $foreground_color . ';font-family:monospace;' ); ?>"
>
	<?php echo esc_html( $barcode_text ); ?>
</span>
</file>

<file path="README.md">
[![CI](https://github.com/woocommerce/woocommerce-order-barcodes/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-order-barcodes/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-order-barcodes/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-order-barcodes/actions/workflows/cron_qit.yml)

woocommerce-order-barcodes
====================

Generates unique barcodes for your orders - perfect for e-tickets, packing slips, reservations and a variety of other uses.

## NPM Scripts

WooCommerce Order Barcodes utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files. This also copies a JS dependency file called onscan.js from the /node_modules/ folder to the /assets/ folder so that it can be minified and used by the plugin.
</file>

<file path=".github/workflows/build.yml">
name: Build
on:
  workflow_call:
    inputs:
      plugin_name:
        required: true
        type: string
        description: 'The name of the plugin (e.g. woocommerce-order-barcodes)'
    outputs:
      plugin_name:
        description: "The name of the plugin"
        value: ${{ jobs.build.outputs.plugin_name }}

jobs:
  build:
    name: Build plugin artifact (zip file)
    runs-on: ubuntu-latest
    outputs:
      plugin_name: ${{ inputs.plugin_name }}
    env:
      PLUGIN_NAME: ${{ inputs.plugin_name }}
    steps:
      - name: Set Git to use HTTPS instead of SSH
        run: git config --global url.https://github.com/.insteadOf git://github.com/

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10

      - uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install PNPM dependencies
        run: pnpm install --frozen-lockfile

      - name: Build plugin zip
        run: pnpm build

      - name: Upload plugin zip
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PLUGIN_NAME }}
          path: ${{ env.PLUGIN_NAME }}.zip
</file>

<file path=".github/workflows/update-requires-headers.yml">
name: Update Requires Headers

on:
  schedule:
    - cron: '0 0 * * *' # Run every day at midnight UTC
  workflow_dispatch: # Allow manual trigger

env:
  PLUGIN_FILE_NAME: "woocommerce-order-barcodes.php"
  MAIN_BRANCH: "trunk"

jobs:
  update-headers:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout Repository
        uses: actions/checkout@v3

      # Step 2: Fetch the latest WooCommerce release
      - name: Fetch Latest WooCommerce Release
        id: fetch_wc_release
        run: |
          # Download the latest WooCommerce release
          echo "Fetching the latest stable release..."
          LATEST_RELEASE=$(curl -s https://api.github.com/repos/woocommerce/woocommerce/releases/latest | jq -r '.tag_name')
          echo "Stable release: $LATEST_RELEASE"
          
          echo "Fetching the latest RC release..."
          RC_RELEASE=$(curl -s https://api.github.com/repos/woocommerce/woocommerce/releases | jq -r '[.[] | select(.tag_name | ascii_downcase | contains("rc"))] | sort_by(.published_at) | reverse | .[0].tag_name')
          echo "RC release: $RC_RELEASE"
          
          # Default to stable release
          DOWNLOAD_TAG="$LATEST_RELEASE"
          
          if [ -n "$RC_RELEASE" ]; then
          # Remove any leading "v" to compare version numbers correctly
          STABLE_VERSION=$(echo "$LATEST_RELEASE" | sed 's/^v//')
          RC_VERSION=$(echo "$RC_RELEASE" | sed 's/^v//')
          
          # Compare versions using sort -V. The highest version will be last.
          HIGHEST=$(echo -e "$RC_VERSION\n$STABLE_VERSION" | sort -V | tail -n 1)
          if [ "$HIGHEST" = "$RC_VERSION" ] && [ "$RC_VERSION" != "$STABLE_VERSION" ]; then
          echo "RC release is newer than stable release."
          DOWNLOAD_TAG="$RC_RELEASE"
          else
          echo "Stable release is newer or equal to RC release."
          fi
          else
          echo "No RC release found."
          fi
          
          echo "Downloading release: $DOWNLOAD_TAG"
          curl -L -o woocommerce.zip "https://github.com/woocommerce/woocommerce/releases/download/$DOWNLOAD_TAG/woocommerce.zip"
          
          # Unzip WooCommerce release
          unzip -q woocommerce.zip
          
          # Extract minor version for WC requires at least
          LATEST_RELEASE=$(echo "$DOWNLOAD_TAG" | sed -E 's/^v?([0-9]+\.[0-9]+\.[0-9]+).*/\1/')
          MAJOR_VERSION=$(echo "$LATEST_RELEASE" | grep -oE '^[0-9]+')
          MINOR_VERSION=$(echo "$LATEST_RELEASE" | grep -oE '^[0-9]+\.[0-9]+' | cut -d. -f2)
          ADJUSTED_MINOR_VERSION=$((MINOR_VERSION - 2))
          
          if [ "$MINOR_VERSION" -le 0 ]; then
            ADJUSTED_MAJOR_VERSION=$((MAJOR_VERSION - 1))
            ADJUSTED_MINOR_VERSION=8
          else
            ADJUSTED_MAJOR_VERSION=$MAJOR_VERSION
            ADJUSTED_MINOR_VERSION=$((MINOR_VERSION - 2))
          fi
          
          LATEST_MINOR_RELEASE="$ADJUSTED_MAJOR_VERSION.$ADJUSTED_MINOR_VERSION"
          
          echo "Latest WooCommerce Release: $LATEST_RELEASE"
          echo "LATEST_RELEASE=$LATEST_RELEASE" >> $GITHUB_ENV
          echo "LATEST_MINOR_RELEASE=$LATEST_MINOR_RELEASE" >> $GITHUB_ENV

      # Step 3: Create and Checkout a New Branch
      - name: Create and Checkout Branch
        run: |
          BRANCH_NAME="tweak/update-requires-headers-${{ env.LATEST_RELEASE }}"
          git checkout -b "$BRANCH_NAME"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV

      # Step 4: Compare and Update Headers
      - name: Compare and Update Headers
        id: compare_and_update
        run: |
          WC_CORE_FILE="woocommerce/woocommerce.php"
          EXTENSION_FILE=${{ env.PLUGIN_FILE_NAME }}
          CHANGES_MADE=false

          # Get required versions from WooCommerce core
          REQUIRES_AT_LEAST=$(grep -oP 'Requires at least: \K[\d.]+' "$WC_CORE_FILE")
          REQUIRES_PHP=$(grep -oP 'Requires PHP: \K[\d.]+' "$WC_CORE_FILE")
          LATEST_MINOR_RELEASE=${{ env.LATEST_MINOR_RELEASE }}
          echo "REQUIRES_AT_LEAST=$REQUIRES_AT_LEAST" >> $GITHUB_ENV
          echo "REQUIRES_PHP=$REQUIRES_PHP" >> $GITHUB_ENV

          # -------------------------------
          # Compare and update 'Requires at least'
          # -------------------------------
          if grep -qE "^\s*\* Requires at least:" "$EXTENSION_FILE"; then
            current_req=$(grep -oP '^\s*\* Requires at least:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'Requires at least' header: $current_req"
            if [ "$(printf "%s\n%s" "$current_req" "$REQUIRES_AT_LEAST" | sort -V | head -n1)" = "$current_req" ] && [ "$current_req" != "$REQUIRES_AT_LEAST" ]; then
              echo "Updating 'Requires at least' header from $current_req to $REQUIRES_AT_LEAST"
              sed -i "s#^\s*\* Requires at least:.*# * Requires at least: $REQUIRES_AT_LEAST#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'Requires at least' ($current_req) is greater than or equal to required ($REQUIRES_AT_LEAST); not updating."
            fi
          else
            echo "Adding missing 'Requires at least' header."
            sed -i "/^\s*\* Version:/a \\ \\* Requires at least: $REQUIRES_AT_LEAST" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          # -------------------------------
          # Compare and update 'Requires PHP'
          # -------------------------------
          if grep -qE "^\s*\* Requires PHP:" "$EXTENSION_FILE"; then
            current_php=$(grep -oP '^\s*\* Requires PHP:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'Requires PHP' header: $current_php"
            if [ "$(printf "%s\n%s" "$current_php" "$REQUIRES_PHP" | sort -V | head -n1)" = "$current_php" ] && [ "$current_php" != "$REQUIRES_PHP" ]; then
              echo "Updating 'Requires PHP' header from $current_php to $REQUIRES_PHP"
              sed -i "s#^\s*\* Requires PHP:.*# * Requires PHP: $REQUIRES_PHP#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'Requires PHP' ($current_php) is greater than or equal to required ($REQUIRES_PHP); not updating."
            fi
          else
            echo "Adding missing 'Requires PHP' header."
            sed -i "/^\s*\* Version:/a \\ \\* Requires PHP: $REQUIRES_PHP" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          # -------------------------------
          # Compare and update 'WC requires at least'
          # -------------------------------
          if grep -qE "^\s*\* WC requires at least:" "$EXTENSION_FILE"; then
            current_wc=$(grep -oP '^\s*\* WC requires at least:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'WC requires at least' header: $current_wc"
            if [ "$(printf "%s\n%s" "$current_wc" "$LATEST_MINOR_RELEASE" | sort -V | head -n1)" = "$current_wc" ] && [ "$current_wc" != "$LATEST_MINOR_RELEASE" ]; then
              echo "Updating 'WC requires at least' header from $current_wc to $LATEST_MINOR_RELEASE"
              sed -i "s#^\s*\* WC requires at least:.*# * WC requires at least: $LATEST_MINOR_RELEASE#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'WC requires at least' ($current_wc) is greater than or equal to required ($LATEST_MINOR_RELEASE); not updating."
            fi
          else
            echo "Adding missing 'WC requires at least' header."
            sed -i "/^\s*\* Version:/a \\ \\* WC requires at least: $LATEST_MINOR_RELEASE" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          echo "EDITED_FILES=${{ env.PLUGIN_FILE_NAME }}" >> $GITHUB_ENV
          echo "CHANGES_MADE=$CHANGES_MADE" >> $GITHUB_ENV

      - name: Compare and Update Headers in README File
        run: |
          README_FILE="readme.txt"
          CHANGES_MADE=${{ env.CHANGES_MADE }}

          if [ -f "$README_FILE" ]; then
            echo "Found $README_FILE. Updating headers..."

            # -------------------------------
            # Compare and update 'Requires at least'
            # -------------------------------
            if grep -qE "^Requires at least:" "$README_FILE"; then
              current_req=$(grep -oP '^Requires at least:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'Requires at least' in README: $current_req"
              if [ "$(printf "%s\n%s" "$current_req" "$REQUIRES_AT_LEAST" | sort -V | head -n1)" = "$current_req" ] && [ "$current_req" != "$REQUIRES_AT_LEAST" ]; then
                echo "Updating 'Requires at least' header to $REQUIRES_AT_LEAST"
                sed -i "s#^Requires at least:.*#Requires at least: $REQUIRES_AT_LEAST#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'Requires at least' ($current_req) is greater than or equal to required ($REQUIRES_AT_LEAST); not updating."
              fi
            else
              echo "Adding missing 'Requires at least' header."
              sed -i "/^Stable tag:/i Requires at least: $REQUIRES_AT_LEAST" "$README_FILE"
              CHANGES_MADE=true
            fi

            # -------------------------------
            # Compare and update 'Requires PHP'
            # -------------------------------
            if grep -qE "^Requires PHP:" "$README_FILE"; then
              current_php=$(grep -oP '^Requires PHP:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'Requires PHP' in README: $current_php"
              if [ "$(printf "%s\n%s" "$current_php" "$REQUIRES_PHP" | sort -V | head -n1)" = "$current_php" ] && [ "$current_php" != "$REQUIRES_PHP" ]; then
                echo "Updating 'Requires PHP' header to $REQUIRES_PHP"
                sed -i "s#^Requires PHP:.*#Requires PHP: $REQUIRES_PHP#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'Requires PHP' ($current_php) is greater than or equal to required ($REQUIRES_PHP); not updating."
              fi
            else
              echo "Adding missing 'Requires PHP' header."
              sed -i "/^Stable tag:/i Requires PHP: $REQUIRES_PHP" "$README_FILE"
              CHANGES_MADE=true
            fi

            # -------------------------------
            # Compare and update 'WC requires at least'
            # -------------------------------
            if grep -qE "^WC requires at least:" "$README_FILE"; then
              current_wc=$(grep -oP '^WC requires at least:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'WC requires at least' in README: $current_wc"
              if [ "$(printf "%s\n%s" "$current_wc" "$LATEST_MINOR_RELEASE" | sort -V | head -n1)" = "$current_wc" ] && [ "$current_wc" != "$LATEST_MINOR_RELEASE" ]; then
                echo "Updating 'WC requires at least' header to $LATEST_MINOR_RELEASE"
                sed -i "s#^WC requires at least:.*#WC requires at least: $LATEST_MINOR_RELEASE#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'WC requires at least' ($current_wc) is greater than or equal to required ($LATEST_MINOR_RELEASE); not updating."
              fi
            else
              echo "Adding missing 'WC requires at least' header."
              sed -i "/^Stable tag:/i WC requires at least: $LATEST_MINOR_RELEASE" "$README_FILE"
              CHANGES_MADE=true
            fi

            echo "CHANGES_MADE=$CHANGES_MADE" >> $GITHUB_ENV
            echo "FILES_TO_CHECK=${{ env.PLUGIN_FILE_NAME }},readme.txt" >> $GITHUB_ENV
          else
            echo "$README_FILE not found."
          fi

      # Step 5: Ignore Unwanted Files
      - name: Ignore Unwanted Files
        run: |
          echo "woocommerce/" >> .gitignore
          echo "woocommerce.zip" >> .gitignore
          git clean -fdx

      # Step 5: Push Branch
      - name: Push Branch
        if: env.CHANGES_MADE == 'true'
        run: |
          git push --set-upstream origin ${{ env.BRANCH_NAME }}

      # Step 6: Create Pull Request
      - name: Create Pull Request
        if: env.CHANGES_MADE == 'true'
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.WCSHIPBOT_GITHUB_TOKEN }}
          branch: ${{ env.BRANCH_NAME }}
          base: ${{ env.MAIN_BRANCH }}
          add-paths: ${{ env.EDITED_FILES }}
          commit-message: "Update Requires headers"
          title: "Update Requires headers for WooCommerce compatibility"
          reviewers: |
            dustinparker
            iyut
            bartech
            abdalsalaam
          body: |
            This PR updates plugin headers to ensure compatibility with the latest WooCommerce release.

            Please test the plugin and merge.
</file>

<file path="includes/class-woocommerce-order-barcodes-settings.php">
<?php
/**
 * Class WooCommerce_Order_Barcodes_Settings.
 *
 * @package woocommerce-order-barcodes
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WooCommerce_Order_Barcodes_Settings.
 *
 * A class to handle the plugin settings.
 */
class WooCommerce_Order_Barcodes_Settings {

	/**
	 * The single instance of WooCommerce_Order_Barcodes_Settings.
	 *
	 * @var     object
	 * @since   1.0.0
	 * @static
	 */
	private static $instance = null;

	/**
	 * The main plugin object.
	 *
	 * @var   object
	 * @since 1.0.0
	 */
	public $parent = null;

	/**
	 * Prefix for plugin settings.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public $base = '';

	/**
	 * Available settings for plugin.
	 *
	 * @var   array
	 * @since 1.0.0
	 */
	public $settings = array();

	/**
	 * WooCommerce settings general object.
	 *
	 * @var WC_Settings_General
	 */
	protected WC_Settings_General $wc_settings_general;

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 * @param WooCommerce_Order_Barcodes $wc_order_barcodes Main plugin object.
	 */
	public function __construct( $wc_order_barcodes ) {

		// Set main plugin class as parent.
		$this->parent = $wc_order_barcodes;

		// Initialise settings.
		$this->init_settings();

		// Set up settings fields.
		add_filter( 'woocommerce_general_settings', array( $this, 'add_settings' ), 10, 1 );
		add_action( 'woocommerce_admin_field_barcode_colors', array( $this, 'colour_settings' ) );
		add_action( 'woocommerce_admin_field_barcode_bgcolor', array( $this, 'bgcolor_settings' ) );
		add_action( 'woocommerce_settings_save_general', array( $this, 'colour_settings_save' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'load_assets' ) );

		// Add settings link to plugins list table.
		add_filter( 'plugin_action_links_' . plugin_basename( $this->parent->file ), array( $this, 'add_settings_link' ) );
	} // End __construct ()

	/**
	 * Initialise settings
	 *
	 * @since  1.0.0
	 * @return void
	 */
	public function init_settings() {
		$this->settings = $this->settings_fields();
	} // End init_settings ()

	/**
	 * Get WooCommerce settings general object.
	 *
	 * @return WC_Settings_General
	 */
	public function get_wc_settings_general(): WC_Settings_General {
		if ( empty( $this->wc_settings_general ) ) {
			$this->wc_settings_general = new WC_Settings_General();
		}

		return $this->wc_settings_general;
	}

	/**
	 * Build settings fields
	 *
	 * @since  1.0.0
	 * @return array Fields to be displayed on settings page
	 */
	private function settings_fields() {

		// Set up available barcode types.
		$type_options = array(
			'code39'     => __( 'Code 39', 'woocommerce-order-barcodes' ),
			'code93'     => __( 'Code 93', 'woocommerce-order-barcodes' ),
			'code128'    => __( 'Code 128', 'woocommerce-order-barcodes' ),
			'datamatrix' => __( 'Data Matrix', 'woocommerce-order-barcodes' ),
			'qr'         => __( 'QR Code', 'woocommerce-order-barcodes' ),
		);

		// Register settings fields.
		$settings = array(
			array(
				'title' => __( 'Order Barcodes', 'woocommerce-order-barcodes' ),
				'type'  => 'title',
				'desc'  => '',
				'id'    => 'order_barcodes',
			),

			array(
				'title'    => __( 'Enable Barcodes', 'woocommerce-order-barcodes' ),
				'desc'     => __( 'This will enable unique barcode generation for each order.', 'woocommerce-order-barcodes' ),
				'id'       => 'wc_order_barcodes_enable',
				'default'  => 'yes',
				'type'     => 'checkbox',
				'class'    => 'checkbox',
				'desc_tip' => true,
			),

			array(
				'title'    => __( 'Barcode Type', 'woocommerce-order-barcodes' ),
				'desc'     => __( 'This is the type of barcode that will be generated for your orders - changing this will only affect future orders.', 'woocommerce-order-barcodes' ),
				'id'       => 'wc_order_barcodes_type',
				'css'      => 'min-width:350px;',
				'default'  => 'code128',
				'type'     => 'select',
				'class'    => 'wc-enhanced-select',
				'desc_tip' => true,
				'options'  => $type_options,
			),

			array( 'type' => 'barcode_colors' ),
			array( 'type' => 'barcode_bgcolor' ),

			array(
				'type' => 'sectionend',
				'id'   => 'order_barcodes',
			),
		);

		/**
		 * Allow settings to be filtered.
		 *
		 * @since 1.0.0
		 */
		$settings = apply_filters( 'wc_order_barcodes_settings_fields', $settings );

		return $settings;
	} // End settings_fields ()

	/**
	 * Markup for colour settings
	 *
	 * @since  1.0.0
	 * @return void
	 */
	public function colour_settings() {
		?>
		<tr valign="top" class="wc_order_barcodes_colours wc_order_barcodes_fields">
			<th scope="row" class="titledesc">
				<label for="wc_order_barcodes_colours_foreground"><?php esc_html_e( 'Barcode Color', 'woocommerce' ); ?></label>
				<?php echo wc_help_tip( __( 'Barcode image and text color', 'woocommerce-order-barcodes' ), true ); //phpcs:ignore --- `wc_help_tip()` has been escaped. ?>
			</th>
			<td class="forminp">
				<?php
				// Get settings.
				$colours = array_map( 'esc_attr', (array) get_option( 'wc_order_barcodes_colours' ) );

				// Set defaults.
				if ( empty( $colours['foreground'] ) ) {
					$colours['foreground'] = '#000000';
				}

				$wc_settings_general = $this->get_wc_settings_general();

				// Show colour selection inputs.
				$wc_settings_general->color_picker( __( 'Foreground', 'woocommerce-order-barcodes' ), 'wc_order_barcodes_colours_foreground', $colours['foreground'] );
				?>
			</td>
		</tr>
		<?php
	} // End colour_settings ().

	/**
	 * Markup for background colour settings
	 *
	 * @since  1.0.0
	 * @return void
	 */
	public function bgcolor_settings() {
		?>
		<tr valign="top" class="wc_order_barcodes_bgcolor wc_order_barcodes_fields">
			<th scope="row" class="titledesc">
				<label for="wc_order_barcodes_colours_background"><?php esc_html_e( 'Background Color', 'woocommerce' ); ?></label>
				<?php echo wc_help_tip( __( 'Background color for barcode', 'woocommerce-order-barcodes' ), true );//phpcs:ignore --- `wc_help_tip()` has been escaped. ?>
			</th>
			<td class="forminp">
				<?php
				// Get settings.
				$colours = array_map( 'esc_attr', (array) get_option( 'wc_order_barcodes_colours' ) );

				// Set defaults.
				if ( empty( $colours['background'] ) ) {
					$colours['background'] = '#ffffff';
				}

				$wc_settings_general = $this->get_wc_settings_general();

				// Show colour selection inputs.
				$wc_settings_general->color_picker( __( 'Background', 'woocommerce-order-barcodes' ), 'wc_order_barcodes_colours_background', $colours['background'] );
				?>
			</td>
		</tr>
		<?php
	} // End colour_settings ().

	/**
	 * Save colour settings.
	 *
	 * @since  1.0.0
	 * @return void
	 */
	public function colour_settings_save() {
		check_admin_referer( 'woocommerce-settings' );

		if ( ! current_user_can( 'manage_woocommerce' ) ) { //phpcs:ignore WordPress.WP.Capabilities.Unknown --- `manage_woocommerce` is a native WC capability
			exit;
		}

		$colours = array();

		if ( isset( $_POST['wc_order_barcodes_colours_foreground'] ) ) {
			// Set settings array.
			$colours['foreground'] = wc_format_hex( sanitize_text_field( wp_unslash( $_POST['wc_order_barcodes_colours_foreground'] ) ) );
		}

		if ( isset( $_POST['wc_order_barcodes_colours_background'] ) ) {
			// Set settings array.
			$colours['background'] = wc_format_hex( sanitize_text_field( wp_unslash( $_POST['wc_order_barcodes_colours_background'] ) ) );
		}

		if ( count( $colours ) > 0 ) {
			// Save settings.
			update_option( 'wc_order_barcodes_colours', $colours );
		}
	} // End colour_settings_save ()

	/**
	 * Add settings to WooCommerce General settings
	 *
	 * @since  1.0.0
	 * @param  array $settings Default settings.
	 * @return array           Modified settings
	 */
	public function add_settings( $settings = array() ) {
		$settings = array_merge( $settings, $this->settings );
		return $settings;
	} // End add_settings ()

	/**
	 * Load assets
	 *
	 * @since  1.0.0
	 * @param  string $hook_suffix Current hook.
	 * @return void
	 */
	public function load_assets( $hook_suffix = '' ) {
		if ( 'woocommerce_page_wc-settings' !== $hook_suffix ) {
			return;
		}
		wp_enqueue_style( $this->parent->token . '-admin' );
	} // End load_assets ()

	/**
	 * Add settings link to plugin list table
	 *
	 * @since  1.0.0
	 * @param  array $links Existing links.
	 * @return array        Modified links
	 */
	public function add_settings_link( $links ) {
		$settings_link = '<a href="' . esc_url( admin_url( 'admin.php?page=wc-settings&tab=general' ) ) . '">' . esc_html__( 'Settings', 'woocommerce-order-barcodes' ) . '</a>';
		array_push( $links, $settings_link );
		return $links;
	} // End add_settings_link ()

	/**
	 * Main class instance - ensures only one instance of the class is loaded or can be loaded.
	 *
	 * @since  1.0.0
	 * @static
	 *
	 * @param  WooCommerce_Order_Barcodes $wc_order_barcodes `WooCommerce_Order_Barcodes` object.
	 * @see    WC_Order_Barcodes()
	 * @return WooCommerce_Order_Barcodes_Settings instance
	 */
	public static function instance( $wc_order_barcodes ) {
		if ( is_null( self::$instance ) ) {
			self::$instance = new self( $wc_order_barcodes );
		}
		return self::$instance;
	} // End instance ()

	/**
	 * Cloning is forbidden
	 *
	 * @since  1.0.0
	 */
	public function __clone() {
		_doing_it_wrong( __FUNCTION__, esc_html__( 'Cheatin&#8217; huh?', 'woocommerce-order-barcodes' ), esc_html( $this->parent->version ) );
	} // End __clone ()

	/**
	 * Unserializing instances of this class is forbidden
	 *
	 * @since  1.0.0
	 */
	public function __wakeup() {
		_doing_it_wrong( __FUNCTION__, esc_html__( 'Cheatin&#8217; huh?', 'woocommerce-order-barcodes' ), esc_html( $this->parent->version ) );
	} // End __wakeup ()
}
</file>

<file path="composer.json">
{
    "name": "woocommerce/woocommerce-order-barcodes",
    "description": "Generates unique barcodes for your orders - perfect for e-tickets, packing slips, reservations and a variety of other uses.",
    "homepage": "http://www.woocommerce.com/products/woocommerce-order-barcodes/",
    "type": "wordpress-plugin",
    "license": "GPL-2.0+",
    "archive": {
        "exclude": [
            "!/assets",
            "!/languages",
			"!/vendor",
            "bin",
            "tests",
            "node_modules",
            "Gruntfile.js",
            "README.md",
            "package.json",
            "package-lock.json",
            "composer.json",
            "composer.lock",
            "phpunit.xml.dist",
            ".*",
            "woocommerce-order-barcodes.zip",
            "*.scss",
            ".phpcs.security.xml",
            "pnpm-lock.yaml"
        ]
    },
    "require": {
		"tecnickcom/tc-lib-barcode": "^1.17"
	},
	"require-dev": {
		"woocommerce/qit-cli": "*",
		"squizlabs/php_codesniffer": "*",
		"dealerdirect/phpcodesniffer-composer-installer": "*",
		"wp-coding-standards/wpcs": "*",
		"woocommerce/woocommerce-sniffs": "*"
	},
	"config": {
		"allow-plugins": {
			"dealerdirect/phpcodesniffer-composer-installer": true
		}
	},
	"scripts": {
		"check-security": [
			"./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=./.phpcs.security.xml  --report-full --report-summary"
		],
		"check-php": [
			"./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"
		],
		"check-php:fix": [
			"./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"
		],
		"check-all": [
			"./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"
		],
		"check-all:fix": [
			"./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"
		],
		"qit:security": [
			"npm run build && composer install && ./vendor/bin/qit run:security woocommerce-order-barcodes --zip=woocommerce-order-barcodes.zip"
		]
	}
}
</file>

<file path="includes/class-woocommerce-order-barcodes.php">
<?php
/**
 * Class WooCommerce_Order_Barcodes file.
 *
 * @package woocommerce-order-barcodes
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

use Milon\Barcode\DNS1D;
use Milon\Barcode\DNS2D;

use WooCommerce\OrderBarcodes\Order_Util;

/**
 * Class WooCommerce_Order_Barcodes.
 *
 * @package woocommerce-order-barcodes
 */
class WooCommerce_Order_Barcodes {
	use Order_Util;

	/**
	 * The single instance of WooCommerce_Order_Barcodes.
	 *
	 * @var   ?self
	 * @since 1.0.0
	 */
	private static ?self $instance = null;

	/**
	 * Settings class object
	 *
	 * @var   ?WooCommerce_Order_Barcodes_Settings
	 * @since 1.0.0
	 */
	public ?WooCommerce_Order_Barcodes_Settings $settings = null;

	/**
	 * The version number.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public string $version;

	/**
	 * The token.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public string $token;

	/**
	 * The main plugin file.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public string $file;

	/**
	 * The main plugin directory.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public string $dir;

	/**
	 * The plugin assets directory.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public string $assets_dir;

	/**
	 * The plugin assets URL.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public string $assets_url;

	/**
	 * Suffix for Javascript.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public string $script_suffix;

	/**
	 * Barcode enabled or not.
	 *
	 * @var   string
	 * @since 1.6.4
	 */
	public string $barcode_enable;

	/**
	 * Type of barcode to be used.
	 *
	 * @var   string
	 * @since 1.0.0
	 */
	public string $barcode_type = 'code128';

	/**
	 * Type of barcode generator class to be used.
	 *
	 * @var WooCommerce_Order_Barcodes_Generator_Tclib.
	 */
	public WooCommerce_Order_Barcodes_Generator_Tclib $barcode_generator;

	/**
	 * Color of barcode.
	 *
	 * @var   array
	 * @since 1.0.0
	 */
	public array $barcode_colours;

	/**
	 * Constructor function.
	 *
	 * @since   1.0.0
	 * @param   string $file    Plugin file.
	 * @param   string $version Plugin version.
	 * @return  void
	 */
	public function __construct( $file = WC_ORDER_BARCODES_FILE, $version = WC_ORDER_BARCODES_VERSION ) {

		// Set plugin data.
		$this->version = $version;
		$this->token   = 'woocommerce_order_barcodes';

		// Set global variables.
		$this->file          = $file;
		$this->dir           = dirname( $this->file );
		$this->assets_dir    = trailingslashit( $this->dir ) . 'assets';
		$this->assets_url    = esc_url( trailingslashit( plugins_url( '/assets/', $this->file ) ) );
		$this->script_suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

		// Apply plugin settings.
		$this->barcode_enable    = get_option( 'wc_order_barcodes_enable', 'yes' );
		$this->barcode_type      = get_option( 'wc_order_barcodes_type', 'code128' );
		$this->barcode_colours   = get_option(
			'wc_order_barcodes_colours',
			array(
				'foreground' => '#000000',
				'background' => '#ffffff',
			)
		);
		$this->barcode_generator = new WooCommerce_Order_Barcodes_Generator_Tclib( $this->barcode_colours['foreground'], $this->barcode_type, $this->barcode_colours['background'] );

		// Register JS.
		add_action( 'admin_enqueue_scripts', array( $this, 'register_admin_assets' ) );
		add_action( 'wp_enqueue_scripts', array( $this, 'register_frontend_assets' ) );

		// Add barcode to order complete email.
		add_action( 'woocommerce_email_after_order_table', array( $this, 'get_email_barcode' ), 1, 1 );

		// Display barcode on order details page.
		add_action( 'woocommerce_order_details_after_order_table', array( $this, 'get_display_barcode' ), 1, 1 );

		// Display barcode on order edit screen.
		add_action( 'add_meta_boxes', array( $this, 'add_order_metabox' ), 30, 2 );

		// Generate and save barcode as order meta.
		add_action( 'woocommerce_new_order', array( $this, 'generate_barcode' ), 1, 1 );
		add_action( 'woocommerce_resume_order', array( $this, 'generate_barcode' ), 1, 1 );

		// Save barcode from order edit screen.
		add_action( 'wp_ajax_save_barcode', array( $this, 'save_barcode' ) );
		add_action( 'wp_ajax_nopriv_save_barcode', array( $this, 'save_barcode' ) );

		// Add shortcode for barcode scanner.
		add_shortcode( 'scan_barcode', array( $this, 'barcode_scan_form' ) );

		// Process barcode input/scan.
		add_action( 'wp_ajax_scan_barcode', array( $this, 'scan_barcode' ) );
		add_action( 'wp_ajax_nopriv_scan_barcode', array( $this, 'scan_barcode' ) );

		// Add check in status drop down to order edit screen.
		add_action( 'woocommerce_admin_order_data_after_order_details', array( $this, 'checkin_status_edit_field' ), 10, 1 );
		add_action( 'woocommerce_process_shop_order_meta', array( $this, 'checkin_status_edit_save' ), 40 );

		/**
		 * Remove the barcode from API responses. Disabled by default. Can use __return_true or __return_false to toggle.
		 *
		 * @since 1.3.1
		 */
		if ( true === apply_filters( 'wc_order_barcodes_remove_image_from_api', false ) ) {
			add_filter( 'woocommerce_rest_prepare_shop_order_object', array( $this, 'remove_barcode_from_api_response' ), null );
		}

		/**
		 * Add barcode url in API responses. Enabled by default. Can use __return_true or __return_false to toggle.
		 *
		 * @since 1.3.24
		 */
		if ( true === apply_filters( 'wc_order_barcodes_add_url_in_api', true ) ) {
			add_filter( 'woocommerce_rest_prepare_shop_order_object', array( $this, 'add_barcode_url_in_api_response' ), 10, 2 );
		}

		add_action( 'init', array( $this, 'get_barcode_image' ), 10, 0 );

		// If OrderUtil does not exists, then use old filter.
		if ( $this->custom_orders_table_usage_is_enabled() ) {
			add_filter( 'woocommerce_order_query_args', array( $this, 'modify_get_orders_query_cot' ), 10, 1 );
		} else {
			add_filter( 'woocommerce_order_data_store_cpt_get_orders_query', array( $this, 'modify_get_orders_query' ), 10, 2 );
		}

		add_filter( 'woocommerce_debug_tools', array( $this, 'add_new_tools_action' ), 10, 1 );
	}

	/**
	 * Add barcode fields to checkout form.
	 *
	 * @since   1.0.0
	 * @since   1.3.19 Only add hidden input for barcode string.
	 * @param   object $checkout Checkout object.
	 * @return  void
	 */
	public function add_checkout_fields( $checkout ) {

		if ( 'yes' !== $this->barcode_enable ) {
			return;
		}

		echo '<input type="hidden" name="order_barcode_text" value="' . esc_attr( $this->get_barcode_string() ) . '" />';
	}

	/**
	 * Add barcode to order meta.
	 *
	 * @since   1.0.0
	 * @param   integer $order_id Order ID.
	 * @return  void
	 */
	public function update_order_meta( $order_id = 0 ) {
		// Only run if barcodes are enabled.
		if ( 'yes' !== $this->barcode_enable ) {
			return;
		}

		// No need to use nonce verification as it has already been verified on `save_barcode()` method.
		$barcode_text = isset( $_POST['order_barcode_text'] ) ? sanitize_text_field( wp_unslash( $_POST['order_barcode_text'] ) ) : '';//phpcs:ignore
		if ( ! empty( $barcode_text ) ) {
			$this->save_order_meta( $order_id, '_barcode_text', $barcode_text );
		}
	}

	/**
	 * Generate unique barcode.
	 *
	 * @since  1.0.0
	 * @param  int $order_id The ID of the order post.
	 * @return void
	 */
	public function generate_barcode( $order_id ) {

		if ( empty( $order_id ) ) {
			return;
		}

		if ( 'yes' !== $this->barcode_enable ) {
			return;
		}

		// Get unqiue barcode string.
		$barcode_string = $this->get_barcode_string();

		$this->save_order_meta( $order_id, '_barcode_text', $barcode_string );
	}

	/**
	 * Save barcode via ajax.
	 *
	 * @since  1.0.0
	 * @return void
	 */
	public function save_barcode() {
		$nonce = ! empty( $_REQUEST['security'] ) ? sanitize_text_field( wp_unslash( $_REQUEST['security'] ) ) : '';

		// phpcs:ignore WordPress.WP.Capabilities.Unknown --- `manage_woocommerce` is a native WC capability.
		if ( ! wp_verify_nonce( $nonce, 'wc_order_barcodes_save_barcode_nonce' ) || ! current_user_can( 'manage_woocommerce' ) || ! isset( $_POST['order_id'] ) ) {
			die( esc_html__( 'Permission denied: Security check failed', 'woocommerce-order-barcodes' ) );
		}

		$this->update_order_meta( intval( $_POST['order_id'] ) );

		exit;
	}

	/**
	 * Get text string for barcode.
	 *
	 * @since  1.0.0
	 * @return string
	 */
	public function get_barcode_string() {

		// Use PHP's uniqid() for the barcode.
		$barcode_string = uniqid();

		// Check if this barcode already exists and add increment if so.
		$existing_order_id = $this->get_barcode_order( $barcode_string );
		$orig_string       = $barcode_string;
		$i                 = 1;
		while ( 0 !== $existing_order_id ) {
			$barcode_string    = $orig_string . $i;
			$existing_order_id = $this->get_barcode_order( $barcode_string );
			++$i;
		}

		/**
		 * Filter to manipulate barcode string.
		 * Return unique barcode.
		 *
		 * @since 1.1.2
		 */
		return apply_filters( $this->token . '_barcode_string', $barcode_string );
	} // End get_barcode_string ()

	/**
	 * Get barcode for display in an email.
	 *
	 * @since  1.0.0
	 * @param  object $order WC_Order object.
	 * @return void
	 */
	public function get_email_barcode( $order ) {
		if ( ! $order ) {
			return;
		}

		/**
		 * Filter to hide barcode display.
		 *
		 * @since 1.3.19
		 */
		if ( ! apply_filters( 'woocommerce_order_barcodes_display_barcode', true ) ) {
			return;
		}

		// Generate correctly formatted HTML for email.
		ob_start(); ?>
		<table cellspacing="0" cellpadding="0" border="0" style="width:100%;border:0;text-align:center;margin-top:20px;margin-bottom:20px;">
			<tbody>
				<tr>
					<td style="text-align:center;vertical-align:middle;word-wrap:normal;">
						<?php
						// The method use `display_barcode()` which already has an escape function.
						echo $this->maybe_display_barcode( $order ); //phpcs:ignore
						?>
					</td>
				</tr>
			</tbody>
		</table>
		<?php
		// Get after text.
		$email = ob_get_clean();

		// The output will only has <table> tag and `maybe_display_barcode()` return value.
		echo $email; //phpcs:ignore
	}

	/**
	 * Get barcode for frontend display
	 *
	 * @since   1.0.0
	 * @param  object $order WC_Order object.
	 * @return void
	 */
	public function get_display_barcode( $order ) {
		if ( ! $order ) {
			return;
		}

		/**
		 * Filter to hide barcode display.
		 *
		 * @since 1.3.19
		 */
		if ( ! apply_filters( 'woocommerce_order_barcodes_display_barcode', true ) ) {
			return;
		}

		// The method use `display_barcode()` which already has escape function.
		$barcode  = '<div class="woocommerce-order-barcodes-container" style="text-align:center;">';
		$barcode .= $this->maybe_display_barcode( $order );
		$barcode .= '</div>';

		echo $barcode; //phpcs:ignore
	}

	/**
	 * Add barcode meta box to order edit screen
	 *
	 * @since   1.0.0
	 * @param   String           $post_type Current post type.
	 * @param   WP_Post|WC_Order $post_or_order_object Either Post object or Order object.
	 * @return  void
	 */
	public function add_order_metabox( $post_type, $post_or_order_object ) {
		if ( ! ( 'shop_order' === $post_type || 'woocommerce_page_wc-orders' === $post_type ) || ! $this->is_order_or_post( $post_or_order_object ) ) {
			return;
		}

		$screen       = $this->get_order_admin_screen();
		$order        = $this->init_theorder_object( $post_or_order_object );
		$barcode_text = $this->get_order_or_post_meta( $order->get_id(), '_barcode_text' );

		if ( 'yes' === $this->barcode_enable || $barcode_text ) {
			add_meta_box( 'woocommerce-order-barcode', __( 'Order Barcode', 'woocommerce-order-barcodes' ), array( $this, 'get_metabox_barcode' ), $screen, 'side', 'default' );
		}
	}

	/**
	 * Get barcode for display in the order metabox
	 *
	 * @since  1.0.0
	 * @param  object $post_or_order_object Order post object.
	 * @return void
	 */
	public function get_metabox_barcode( $post_or_order_object ) {
		if ( ! $this->is_order_or_post( $post_or_order_object ) ) {
			return;
		}

		$order        = $this->init_theorder_object( $post_or_order_object );
		$barcode_text = $this->get_order_or_post_meta( $order->get_id(), '_barcode_text' );

		wp_enqueue_style( $this->token . '-admin' );

		if ( ! $barcode_text ) {
			$this->generate_barcode( $order->get_id() );
		}

		echo '<div class="woocommerce-order-barcodes-container" style="text-align:center;">';
		// The method use `display_barcode()` which already has escape function.
		echo $this->maybe_display_barcode( $order ); //phpcs:ignore
		echo '</div>';
	}

	/**
	 * Maybe displaying barcode.
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @return String.
	 */
	public function maybe_display_barcode( $order ) {
		return ( ! wc_is_order_status( $this->get_order_status( $order ) ) ) ? $this->barcode_not_ready_error_text() : $this->display_barcode( $order->get_id(), true );
	}

	/**
	 * Display barcode as an image
	 *
	 * @since 1.0.0
	 *
	 * @param integer $order_id Order ID.
	 * @param boolean $image    Display as an image (default false).
	 * @return string The generated barcode.
	 */
	public function display_barcode( $order_id = 0, $image = false ) {
		if ( ! $order_id ) {
			return '';
		}

		// Get barcode text.
		$barcode_text = $this->get_order_or_post_meta( $order_id, '_barcode_text' );

		if ( ! $barcode_text ) {
			return esc_html__( 'Barcode does not exist!', 'woocommerce-order-barcodes' );
		}

		$foreground_color = $this->barcode_colours['foreground'];
		$background_color = $this->barcode_colours['background'];

		// Return an image (for emails and frontend order view).
		if ( $image ) {
			$barcode_url = $this->barcode_url( $order_id );

			ob_start();
			echo '<table cellpadding="0" cellspacing="0" border="0" style="width:200px; margin:0px auto;">';
				echo '<tr>';
				echo '<td style="text-align:center; padding:13px; background-color:' . esc_attr( $background_color ) . ';">';
				require WC_ORDER_BARCODES_DIR_PATH . '/templates/barcode-image.php';
				echo '</td>';
				echo '</tr>';
			echo '</table>';
			return ob_get_clean();
		}

		$barcode = '<div class="woocommerce-order-barcodes-container" style="text-align:center;justify-content: center;display:inline-block;margin-top:5px;padding:7px;background-color:' . esc_attr( $background_color ) . ';">';

		// Generate barcode image based on string and selected type. And use SVG for datamatrix.
		$barcode_output = ( 'datamatrix' === $this->barcode_type ) ? 'SVG' : 'HTML';
		$barcode       .= $this->barcode_generator->get_generated_barcode( $barcode_text, $barcode_output );

		$barcode .= '<br /><span class="woocommerce-order-barcodes-number" style="color:' . esc_attr( $this->barcode_colours['foreground'] ) . ';font-family:monospace;position:relative;top:-12px;">' . esc_html( $barcode_text ) . '</span>';

		$barcode .= '</div>';

		return $barcode;
	}

	/**
	 * Get the URL for a given order's barcode
	 *
	 * @since 1.0.0
	 *
	 * @param  integer $order_id Order ID.
	 * @return string  URL for barcode.
	 */
	public function barcode_url( $order_id = 0 ) {

		if ( ! $order_id ) {
			return;
		}

		// Get barcode text.
		$barcode_text = $this->get_order_or_post_meta( $order_id, '_barcode_text' );
		return trailingslashit( get_site_url() ) . '?wc_barcode=' . $barcode_text;
	}

	/**
	 * Form for scanning barcodes
	 *
	 * @param array $params Shortcode parameters.
	 * @return string Form markup
	 */
	public function barcode_scan_form( $params = array() ) {
		/**
		 * Filter to check if user has barcode scanning permissions.
		 *
		 * @since 1.1.0
		 */
		$can_scan = apply_filters( $this->token . '_scan_permission', current_user_can( 'manage_woocommerce' ), 0 ); // phpcs:ignore WordPress.WP.Capabilities.Unknown --- `manage_woocommerce` is a native WC capability.
		if ( ! $can_scan ) {
			return;
		}

		// Get shortcode parameters.
		$atts = shortcode_atts(
			array(
				'action' => '',
			),
			$params
		);

		$action = esc_html( $atts['action'] );

		// Add .woocommerce class as CSS namespace.
		$html = '<div class="woocommerce">';

			// Create form.
			$html .= '<div id="barcode-scan-form">
						<form name="barcode-scan" action="" method="post">
							<select name="scan-action" id="scan-action" class="scan_action" required>
								<option value="" ' . selected( $action, '', false ) . '>' . __( 'Select action', 'woocommerce-order-barcodes' ) . '</option>
								<option value="lookup" ' . selected( $action, 'lookup', false ) . '>' . __( 'Look up', 'woocommerce-order-barcodes' ) . '</option>
								<option value="complete" ' . selected( $action, 'complete', false ) . '>' . __( 'Complete order', 'woocommerce-order-barcodes' ) . '</option>
								<option value="checkin" ' . selected( $action, 'checkin', false ) . '>' . __( 'Check in', 'woocommerce-order-barcodes' ) . '</option>
								<option value="checkout" ' . selected( $action, 'checkout', false ) . '>' . __( 'Check out', 'woocommerce-order-barcodes' ) . '</option>
							</select>

							<input type="text" name="scan-code" id="scan-code" value="" placeholder="' . __( 'Scan or enter barcode', 'woocommerce-order-barcodes' ) . '" required />

							<input type="submit" value="' . esc_html__( 'Go', 'woocommerce-order-barcodes' ) . '" />
						</form>
					  </div>';

			// Add loading text.
			$html .= '<div id="barcode-scan-loader">' . __( 'Processing barcode...', 'woocommerce-order-barcodes' ) . '</div>';

			// Add empty div for scan results to be loaded via ajax.
			$html .= '<div id="barcode-scan-result"></div>';

		$html .= '</div>';

		// Load necessary JS & CSS.
		$this->load_barcode_assets();

		return $html;
	} // End barcode_scan_form ()

	/**
	 * Process scanning/input of barcode
	 *
	 * @return void
	 */
	public function scan_barcode() {
		/**
		 * Filter to bypass nonce check.
		 *
		 * @since 1.1.0
		 */
		$do_nonce_check = apply_filters( $this->token . '_do_nonce_check', true );
		$scan_nonce     = isset( $_POST[ $this->token . '_scan_nonce' ] ) ? sanitize_text_field( wp_unslash( $_POST[ $this->token . '_scan_nonce' ] ) ) : '';
		if ( $do_nonce_check && ! wp_verify_nonce( $scan_nonce, 'scan-barcode' ) ) {
			$this->display_notice( __( 'Permission denied: Security check failed', 'woocommerce-order-barcodes' ), 'error' );
			exit;
		}

		// Retrieve order ID from barcode.
		$barcode_input = isset( $_POST['barcode_input'] ) ? sanitize_text_field( wp_unslash( $_POST['barcode_input'] ) ) : '';

		if ( empty( $barcode_input ) ) {
			$this->display_notice( __( 'Invalid barcode', 'woocommerce-order-barcodes' ), 'error' );
			exit;
		}

		$order_id = $this->get_barcode_order( $barcode_input );
		if ( ! $order_id ) {
			$this->display_notice( __( 'Invalid barcode', 'woocommerce-order-barcodes' ), 'error' );
			exit;
		}

		/**
		 * Check if user has barcode scanning permissions.
		 *
		 * @since 1.1.0
		 */
		$can_scan = apply_filters( $this->token . '_scan_permission', current_user_can( 'manage_woocommerce' ), $order_id ); // phpcs:ignore WordPress.WP.Capabilities.Unknown --- `manage_woocommerce` is a native WC capability.
		if ( ! $can_scan ) {
			$this->display_notice( __( 'Permission denied: You do not have sufficient permissions to scan barcodes', 'woocommerce-order-barcodes' ), 'error' );
			exit;
		}

		// Get order object.
		$order = wc_get_order( $order_id );

		if ( ! is_a( $order, 'WC_Order' ) || is_wp_error( $order ) ) {
			$this->display_notice( __( 'Invalid order ID', 'woocommerce-order-barcodes' ), 'error' );
			exit;
		}

		$response_type = 'success';

		// Get selected action and process accordingly.
		$action = isset( $_POST['scan_action'] ) ? sanitize_text_field( wp_unslash( $_POST['scan_action'] ) ) : '';
		switch ( $action ) {
			case 'complete':
				/**
				 * Filter that can be used to skip order status updates.
				 *
				 * @since 1.0.0
				 */
				if ( apply_filters( $this->token . '_complete_order', true, $order_id ) ) {
					if ( 'completed' === $order->get_status() ) {
						$response      = __( 'Order already completed', 'woocommerce-order-barcodes' );
						$response_type = 'notice';
					} else {
						$order->update_status( 'completed' );
						$response = __( 'Order marked as complete', 'woocommerce-order-barcodes' );
						$order    = new WC_Order( $order_id );
					}
				} else {
					$response      = __( 'Not able to complete order', 'woocommerce-order-barcodes' );
					$response_type = 'error';
				}
				break;

			case 'checkin':
				if ( 'yes' === $this->get_order_or_post_meta( $order_id, '_checked_in' ) ) {
					$response      = esc_html__( 'Customer already checked in', 'woocommerce-order-barcodes' );
					$response_type = 'notice';
				} else {
					$this->save_order_meta( $order_id, '_checked_in', 'yes' );
					$response = esc_html__( 'Customer has checked in', 'woocommerce-order-barcodes' );
				}
				break;

			case 'checkout':
				if ( 'no' === $this->get_order_or_post_meta( $order_id, '_checked_in' ) ) {
					$response      = esc_html__( 'Customer already checked out', 'woocommerce-order-barcodes' );
					$response_type = 'notice';
				} else {
					$this->save_order_meta( $order_id, '_checked_in', 'no' );
					$response = esc_html__( 'Customer has checked out', 'woocommerce-order-barcodes' );
				}
				break;

			case 'lookup':
				// translators: %s is Order ID.
				$response = sprintf( __( 'Found matched order: #%s', 'woocommerce-order-barcodes' ), $order_id );
				break;

			default:
				$response      = __( 'Please select an action to perform', 'woocommerce-order-barcodes' );
				$response_type = 'error';
				break;
		}

		// Display response notice.
		if ( $response ) {
			$this->display_notice( $response, $response_type );
		}

		// No need to display order info if response_type is 'error'.
		if ( 'error' === $response_type ) {
			exit;
		}

		// Display check-in status if set.
		$checked_in = $this->get_order_or_post_meta( $order_id, '_checked_in' );
		if ( $checked_in ) {
			$checkin_status = ( 'yes' === $checked_in ) ? esc_html__( 'Checked in', 'woocommerce-order-barcodes' ) : esc_html__( 'Checked out', 'woocommerce-order-barcodes' );
			echo '<h3 class="checked_in ' . esc_attr( $checked_in ) . '">' . esc_html( $checkin_status ) . '</h3>';
		}

		// Display order details template.
		wc_get_template(
			'myaccount/view-order.php',
			array(
				'status'   => get_term_by( 'slug', $order->get_status(), 'shop_order_status' ),
				'order'    => $order,
				'order_id' => $order_id,
			)
		);

		// Exit function to prevent '0' displaying at the end of ajax request.
		exit;
	} // End scan_barcode ()

	/**
	 * Display custom WooCommerce notice.
	 *
	 * @param string $message Message text.
	 * @param string $type    Type of notice.
	 *
	 * @return void
	 */
	public function display_notice( $message = '', $type = 'success' ) {

		if ( ! $message ) {
			return;
		}

		// Display notice template.
		echo '<div class="woocommerce-' . esc_attr( $type ) . '" role="alert">' . wc_kses_notice( $message ) . '</div>';// phpcs:ignore
	}

	/**
	 * Retrieve order ID from barcode.
	 *
	 * @param string $barcode Scanned barcode.
	 *
	 * @return integer Order ID
	 */
	public function get_barcode_order_before_wc_310( $barcode = '' ) {

		if ( ! $barcode ) {
			return 0;
		}

		// Set up query with using meta key and meta value.
		// phpcs:disable
		$args = array(
			'post_type'      => 'shop_order',
			'posts_per_page' => 1,
			'meta_key'       => '_barcode_text',
			'meta_value'     => $barcode,
			'post_status'    => array_keys( wc_get_order_statuses() ),
		);
		// phpcs:enable

		// Get orders.
		$orders = get_posts( $args );

		// Get order ID.
		$order_id = 0;
		if ( 0 < count( $orders ) ) {
			foreach ( $orders as $order ) {
				$order_id = $order->ID;
				break;
			}
		}

		return $order_id;
	} // End get_barcode_order ()

	/**
	 * Retrieve order ID from barcode.
	 *
	 * @param string $barcode Scanned barcode.
	 *
	 * @return integer Order ID
	 */
	public function get_barcode_order( $barcode = '' ) {

		if ( ! $barcode ) {
			return 0;
		}

		if ( version_compare( WC()->version, '3.1.0', '<' ) ) {
			return $this->get_barcode_order_before_wc_310( $barcode );
		}

		$args = array(
			'get_barcode_text' => $barcode,
			'limit'            => 1,
		);

		// Get orders.
		$orders = wc_get_orders( $args );

		// Get order ID.
		$order_id = 0;
		if ( 0 < count( $orders ) ) {
			foreach ( $orders as $order ) {
				$order_id = $order->get_id();
				break;
			}
		}

		return $order_id;
	} // End get_barcode_order ()

	/**
	 * Display check in status field on order edit screen
	 *
	 * @since  1.0.0
	 * @param  WC_Order $order WC_Order object.
	 * @return void
	 */
	public function checkin_status_edit_field( $order ) {
		$order_id   = $order->get_id();
		$checked_in = $this->get_order_or_post_meta( $order_id, '_checked_in' );

		if ( $checked_in ) {
			?>
			<p class="form-field form-field-wide"><label for="checkin_status"><?php esc_html_e( 'Check in status:', 'woocommerce-order-barcodes' ); ?></label>
			<select id="checkin_status" name="checkin_status">
				<option value="<?php esc_attr_e( 'yes' ); ?>" <?php selected( 'yes', $checked_in, true ); ?>><?php esc_html_e( 'Checked in', 'woocommerce-order-barcodes' ); ?></option>
				<option value="<?php esc_attr_e( 'no' ); ?>" <?php selected( 'no', $checked_in, true ); ?>><?php esc_html_e( 'Checked out', 'woocommerce-order-barcodes' ); ?></option>
			</select></p>
			<?php
		}
	} // End checkin_status_edit_field ()

	/**
	 * Save check in status on order edit screen
	 *
	 * @since 1.0.0
	 * @param integer $post_id Order post ID.
	 *
	 * @return  void
	 */
	public function checkin_status_edit_save( $post_id ) {
		// No need to use nonce. It has been done before the action take place.
		$checkin_status = isset( $_POST['checkin_status'] ) ? sanitize_text_field( wp_unslash( $_POST['checkin_status'] ) ) : ''; // phpcs:ignore
		if ( ! empty( $checkin_status ) ) {
			$this->save_order_meta( $post_id, '_checked_in', $checkin_status );
		}
	} // End checkin_status_edit_save ()

	/**
	 * Remove the _barcode_image metadata from REST API responses.
	 *
	 * @since 1.3.1
	 * @param WP_REST $response WP_REST_Response.
	 *
	 * @return  object.
	 */
	public function remove_barcode_from_api_response( $response ) {
		if ( is_a( $response, 'WP_REST_Response' ) && isset( $response->data['meta_data'] ) ) {
			if ( 0 < count( $response->data['meta_data'] ) ) {
				foreach ( $response->data['meta_data'] as $k => $v ) {
					if ( '_barcode_image' === $v->key ) {
						unset( $response->data['meta_data'][ $k ] );
					}
				}
			}
		}
		return $response;
	}

	/**
	 * Add the barcode URL in REST API responses.
	 *
	 * @since   1.3.24
	 * @param   WP_REST  $response       WP_REST_Response.
	 * @param   WC_Order $maybe_wc_order WC_Order Object.
	 *
	 * @return  object  $response
	 */
	public function add_barcode_url_in_api_response( $response, $maybe_wc_order ) {
		if ( is_a( $response, 'WP_REST_Response' ) && is_a( $maybe_wc_order, 'WC_Order' ) ) {
			$barcode_url = $this->barcode_url( $maybe_wc_order->get_id() );

			if ( ! empty( $barcode_url ) ) {
				$response->data['barcode_url'] = $barcode_url;
			}
		}
		return $response;
	}

	/**
	 * Register all required JS & CSS for admin.
	 *
	 * @since   1.0.0
	 * @since   1.3.19 Isolate to admin assets.
	 * @return  void
	 */
	public function register_admin_assets() {
		wp_register_style( $this->token . '-admin', esc_url( $this->assets_url ) . 'css/admin.css', array(), $this->version );
	}

	/**
	 * Load onscan js library.
	 *
	 * @since 1.3.19
	 * @return void
	 */
	public function load_onscan_js() {
		wp_enqueue_script( $this->token . '-frontend-onscan', esc_url( plugins_url( '/assets/js/', $this->file ) ) . 'onscan' . $this->script_suffix . '.js', array( 'jquery' ), $this->version, true );
	}

	/**
	 * Register all required JS & CSS for frontend.
	 *
	 * @since   1.0.0
	 * @since   1.3.19 Isolate to frontend assets.
	 * @return  void
	 */
	public function register_frontend_assets() {
		wp_register_script( $this->token . '-frontend', esc_url( $this->assets_url ) . 'js/frontend' . $this->script_suffix . '.js', array( 'jquery', $this->token . '-frontend-onscan' ), $this->version, true );

		wp_register_style( $this->token . '-frontend', esc_url( $this->assets_url ) . 'css/frontend.css', array(), $this->version );

		// Pass data to frontend JS.
		wp_localize_script(
			$this->token . '-frontend',
			'wc_order_barcodes',
			array(
				'ajaxurl'    => admin_url( 'admin-ajax.php' ),
				'scan_nonce' => wp_create_nonce( 'scan-barcode' ),
			)
		);
	}

	/**
	 * Load JS & CSS required for barcode generation
	 *
	 * @since   1.0.0
	 * @since   1.3.19 Remove deprecated qr code script.
	 * @return  void
	 */
	public function load_barcode_assets() {
		$this->load_onscan_js();
		wp_enqueue_script( $this->token . '-frontend' );

		if ( ! is_admin() ) {
			wp_enqueue_style( $this->token . '-frontend' );
		}
	}

	/**
	 * Get barcode image.
	 *
	 * @since   1.0.0
	 * @return  void
	 */
	public function get_barcode_image() {
		$barcode = isset( $_GET['wc_barcode'] ) ? wc_clean( wp_unslash( $_GET['wc_barcode'] ) ) : ''; // phpcs:ignore
		if ( empty( $barcode ) ) {
			return;
		}

		// New url format uses generated uniq_id which is not easy to guess.
		$order_id = $this->get_barcode_order( $barcode );

		if ( ! $order_id ) {
			// Either wrong order or this may be a Box Office Ticket.
			if ( class_exists( 'WC_Box_Office' ) ) {
				$order_id = WCBO()->components->ticket_barcode->get_ticket_id_from_barcode_text( $barcode );
			}
		}

		// Check if barcode is an order id.
		$order = wc_get_order( $barcode );

		if ( ! $order_id && is_a( $order, 'WC_Order' ) ) {
			$order_id = $barcode;

			// phpcs:ignore WordPress.WP.Capabilities.Unknown --- `manage_woocommerce` and `view_order` is native WC capability.
			if ( ! ( current_user_can( 'manage_woocommerce' ) || current_user_can( 'view_order', $order_id ) ) ) {
				return;
			}
		}

		if ( ! $order_id ) {
			return;
		}

		// Generate barcode image based on string and selected type.
		$barcode_img = $this->barcode_generator->get_generated_barcode( $barcode, 'PNG' );
		$foreground  = $this->hex_to_rgb( $this->barcode_colours['foreground'] );

		// Set headers for image output.
		if ( ini_get( 'zlib.output_compression' ) ) {
			ini_set( 'zlib.output_compression', 'Off' ); // phpcs:ignore --- Necessary for image output.
		}

		header( 'Pragma: public' );
		header( 'Expires: 0' );
		header( 'Cache-Control: must-revalidate, post-check=0, pre-check=0' );
		header( 'Cache-Control: private', false );
		header( 'Content-Transfer-Encoding: binary' );
		header( 'Content-Type: image/png' );

		// Binary value. Doesn't need to escape this.
		exit( $barcode_img );// phpcs:ignore
	}

	/**
	 * Convert hexidecimal colour to RGB.
	 *
	 * @since 1.3.21
	 *
	 * @param string $hex Hexidecimal colour code.
	 * @return array RGB colours.
	 */
	private function hex_to_rgb( $hex ) {
		$hex = ltrim( $hex, '#' );
		switch ( strlen( $hex ) ) {
			case 3:
				list( $r, $g, $b ) = sscanf( $hex, '%1s%1s%1s' );
				return array( hexdec( "$r$r" ), hexdec( "$g$g" ), hexdec( "$b$b" ) );
			case 6:
				return array_map( 'hexdec', sscanf( $hex, '%2s%2s%2s' ) );
		}

		return array( 0, 0, 0 ); // Default black.
	}

	/**
	 * Add new tools action button.
	 *
	 * @param array $tools List of tools action.
	 *
	 * @result array
	 */
	public function add_new_tools_action( $tools ) {
		$tools['generate_barcode_orders'] = array(
			'name'     => esc_html__( 'Generate Barcodes', 'woocommerce-order-barcodes' ),
			'button'   => esc_html__( 'Generate', 'woocommerce-order-barcodes' ),
			'desc'     => esc_html__( 'Generate the barcode for existing orders.', 'woocommerce-order-barcodes' ),
			'callback' => array( $this, 'generate_barcode_for_existing_orders' ),
		);

		return $tools;
	}

	/**
	 * Save Order meta.
	 *
	 * @param int    $order_id The ID of the order post.
	 * @param String $meta_name The name of the meta.
	 * @param Mixed  $meta_value The value of the meta.
	 */
	public function save_order_meta( $order_id, $meta_name, $meta_value ) {
		$order = wc_get_order( $order_id );

		if ( ! is_a( $order, 'WC_Order' ) ) {
			return false;
		}

		$order->update_meta_data( $meta_name, $meta_value );
		$order->save();
	}

	/**
	 * Get Order or post meta value.
	 *
	 * @param int    $id The ID of the order or post.
	 * @param String $meta_name The name of the meta.
	 */
	public function get_order_or_post_meta( $id, $meta_name ) {
		$order = wc_get_order( $id );

		if ( $this->is_wc_order( $order ) ) {
			return $order->get_meta( $meta_name );
		}

		// Check the post too for WC Box Office compatibility.
		$post = get_post( $id );

		if ( $this->is_wp_post( $post ) ) {
			return get_post_meta( $id, $meta_name, true );
		}

		return false;
	}

	/**
	 * Generate barcode for existing orders before WC version 3.1.0.
	 */
	public function generate_barcode_for_existing_orders_before_wc_310() {
		// Set up query.
		$args = array(
			'post_type'      => 'shop_order',
			'posts_per_page' => -1,
			// phpcs:disable
			'meta_query'     => array(
				array(
					'key'     => '_barcode_text',
					'compare' => 'NOT EXISTS',
				),
			),
			// phpcs:enable
			'post_status'    => array_keys( wc_get_order_statuses() ),
		);

		// Get orders.
		$orders = get_posts( $args );

		// Get order ID.
		$order_id = 0;
		if ( ! empty( $orders ) ) {
			foreach ( $orders as $order ) {
				$this->generate_barcode( $order->ID );
			}
		}
	}

	/**
	 * Generate barcode for existing orders.
	 */
	public function generate_barcode_for_existing_orders() {
		if ( version_compare( WC_VERSION, '3.1.0', '<' ) ) {
			$this->generate_barcode_for_existing_orders_before_wc_310();
			return;
		}

		$result = wc_get_orders(
			array(
				'barcode_not_exists' => true,
				'limit'              => -1,
			)
		);

		if ( ! empty( $result ) ) {
			foreach ( $result as $order ) {
				$this->generate_barcode( $order->get_id() );
			}
		}
	}

	/**
	 * Modify the wc_get_orders query.
	 *
	 * @param array $query_vars Query variable.
	 *
	 * @return array
	 */
	public function modify_get_orders_query_cot( $query_vars ) {
		if ( ! empty( $query_vars['barcode_not_exists'] ) ) {
			$query_vars['meta_query'][] = array(
				'key'     => '_barcode_text',
				'compare' => 'NOT EXISTS',
			);
		}

		if ( ! empty( $query_vars['get_barcode_text'] ) ) {
			$query_vars['meta_query'][] = array(
				'key'   => '_barcode_text',
				'value' => esc_attr( $query_vars['get_barcode_text'] ),
			);
		}

		return $query_vars;
	}

	/**
	 * Modify the wc_get_orders query.
	 *
	 * @param array $query Query variable.
	 * @param array $query_vars Query variable.
	 *
	 * @return array
	 */
	public function modify_get_orders_query( $query, $query_vars ) {
		if ( ! empty( $query_vars['barcode_not_exists'] ) ) {
			$query['meta_query'][] = array(
				'key'     => '_barcode_text',
				'compare' => 'NOT EXISTS',
			);
		}

		if ( ! empty( $query_vars['get_barcode_text'] ) ) {
			$query['meta_query'][] = array(
				'key'   => '_barcode_text',
				'value' => esc_attr( $query_vars['get_barcode_text'] ),
			);
		}

		return $query;
	}

	/**
	 * Sanitize the barcode html using `wp_kses()`.
	 *
	 * @param string $barcode_text Barcode HTML text.
	 *
	 * @return string.
	 */
	public function sanitize_barcode_html( $barcode_text ) {
		$allowed_html = array(
			'img'   => array(
				'src'   => array(),
				'title' => array(),
				'alt'   => array(),
				'style' => array(),
			),
			'svg'   => array(
				'xmlns'       => array(),
				'fill'        => array(),
				'viewbox'     => array(),
				'role'        => array(),
				'aria-hidden' => array(),
				'focusable'   => array(),
				'height'      => array(),
				'width'       => array(),
			),
			'path'  => array(
				'd'    => array(),
				'fill' => array(),
			),
			'div'   => array(
				'style' => array(),
				'class' => array(),
			),
			'span'  => array(
				'style' => array(),
				'class' => array(),
			),
			'br'    => array(),
			'table' => array(
				'class'       => array(),
				'id'          => array(),
				'cellspacing' => array(),
				'cellpadding' => array(),
				'border'      => array(),
				'style'       => array(),
			),
			'thead' => array(
				'style' => array(),
				'class' => array(),
			),
			'tbody' => array(
				'style' => array(),
				'class' => array(),
			),
			'tfoot' => array(
				'style' => array(),
				'class' => array(),
			),
			'tr'    => array(
				'style' => array(),
				'class' => array(),
			),
			'td'    => array(
				'style' => array(),
				'class' => array(),
			),
		);

		return wp_kses( $barcode_text, $allowed_html );
	}

	/**
	 * Main class instance - ensures only one instance of the class is loaded or can be loaded
	 *
	 * @since  1.0.0
	 * @static
	 * @see    WC_Order_Barcodes()
	 * @param string $file Path of file.
	 * @param string $version Version of the plugin.
	 *
	 * @return Main WooCommerce_Order_Barcodes instance
	 */
	public static function instance( $file = WC_ORDER_BARCODES_FILE, $version = WC_ORDER_BARCODES_VERSION ) {
		if ( is_null( self::$instance ) ) {
			self::$instance = new self( $file, $version );
		}
		return self::$instance;
	} // End instance ()

	/**
	 * Cloning is forbidden.
	 *
	 * @since  1.0.0
	 */
	public function __clone() {
		_doing_it_wrong( __FUNCTION__, esc_html__( 'Cheatin&#8217; huh?', 'woocommerce-order-barcodes' ), esc_html( $this->version ) );
	} // End __clone ()

	/**
	 * Unserializing instances of this class is forbidden.
	 *
	 * @since  1.0.0
	 */
	public function __wakeup() {
		_doing_it_wrong( __FUNCTION__, esc_html__( 'Cheatin&#8217; huh?', 'woocommerce-order-barcodes' ), esc_html( $this->version ) );
	} // End __wakeup ()
}
</file>

<file path="woocommerce-order-barcodes.php">
<?php
/**
 * Plugin Name: WooCommerce Order Barcodes
 * Version: 1.9.4
 * Plugin URI: https://woocommerce.com/products/woocommerce-order-barcodes/
 * Description: Generates unique barcodes for your orders - perfect for e-tickets, packing slips, reservations and a variety of other uses.
 * Author: WooCommerce
 * Author URI: https://woocommerce.com/
 * Text Domain: woocommerce-order-barcodes
 * Domain Path: /languages
 * Requires Plugins: woocommerce
 * Requires PHP: 7.4
 * Requires at least: 6.7
 * Tested up to: 6.8
 * WC requires at least: 9.9
 * WC tested up to: 10.1
 * Woo: 391708:889835bb29ee3400923653e1e44a3779
 *
 * @package woocommerce-order-barcodes
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

define( 'WC_ORDER_BARCODES_VERSION', '1.9.4' ); // WRCS: DEFINED_VERSION.
define( 'WC_ORDER_BARCODES_FILE', __FILE__ );
define( 'WC_ORDER_BARCODES_DIR_PATH', untrailingslashit( plugin_dir_path( WC_ORDER_BARCODES_FILE ) ) );
define( 'WC_ORDER_BARCODES_DIR_URL', untrailingslashit( plugins_url( '/', WC_ORDER_BARCODES_FILE ) ) );

// Activation hook.
register_activation_hook( __FILE__, 'wc_order_barcodes_activate' );

/**
 * Activation function.
 */
function wc_order_barcodes_activate() {
	update_option( 'woocommerce_order_barcodes_version', WC_ORDER_BARCODES_VERSION );
}

// Plugin init hook.
add_action( 'plugins_loaded', 'wc_order_barcodes_init' );

/**
 * Initialize plugin.
 */
function wc_order_barcodes_init() {

	if ( ! class_exists( 'WooCommerce' ) ) {
		add_action( 'admin_notices', 'wc_order_barcodes_woocommerce_deactivated' );
		return;
	}

	// Initialise plugin.
	add_action( 'before_woocommerce_init', 'wc_order_barcodes_load_classes', 5 );
	add_action( 'before_woocommerce_init', 'wc_order_barcodes_compatibility_declaration' );
	add_action( 'before_woocommerce_init', 'WC_Order_Barcodes', 15 );
	add_action( 'after_setup_theme', 'wc_order_barcodes_load_textdomain' );
}

/**
 * Load plugin classes.
 */
function wc_order_barcodes_load_classes() {
	// Autoload.
	require_once WC_ORDER_BARCODES_DIR_PATH . '/vendor/autoload.php';

	// Include order util trait class file.
	require_once WC_ORDER_BARCODES_DIR_PATH . '/includes/trait-woocommerce-order-util.php';

	// Include barcode generator files.
	require_once WC_ORDER_BARCODES_DIR_PATH . '/lib/barcode_generator/class-woocommerce-order-barcodes-generator-tclib.php';

	// Include plugin class files.
	require_once WC_ORDER_BARCODES_DIR_PATH . '/includes/class-woocommerce-order-barcodes.php';
	require_once WC_ORDER_BARCODES_DIR_PATH . '/includes/class-woocommerce-order-barcodes-settings.php';

	// Include plugin functions file.
	require_once WC_ORDER_BARCODES_DIR_PATH . '/includes/woocommerce-order-barcodes-functions.php';

	if ( is_admin() ) {
		require_once WC_ORDER_BARCODES_DIR_PATH . '/includes/class-woocommerce-order-barcodes-privacy.php';
	}
}

/**
 * WooCommerce feature compatibility declaration.
 *
 * @return void
 */
function wc_order_barcodes_compatibility_declaration() {
	if ( ! class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
		return;
	}

	// Declare High-Performance Order Storage (HPOS) compatibility
	// See https://github.com/woocommerce/woocommerce/wiki/High-Performance-Order-Storage-Upgrade-Recipe-Book#declaring-extension-incompatibility.
	\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', WC_ORDER_BARCODES_FILE );

	// Declare Cart/Checkout Blocks compatibility.
	// See https://developer.woocommerce.com/2023/08/18/cart-and-checkout-blocks-becoming-the-default-experience/.
	\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'cart_checkout_blocks', WC_ORDER_BARCODES_FILE );
}

/**
 * Load localization.
 */
function wc_order_barcodes_load_textdomain() {
	load_plugin_textdomain( 'woocommerce-order-barcodes', false, basename( WC_ORDER_BARCODES_DIR_PATH ) . '/languages' );
}

/**
 * WooCommerce Deactivated Notice.
 */
function wc_order_barcodes_woocommerce_deactivated() {
	/* translators: %s: WooCommerce link */
	echo '<div class="error"><p>' . sprintf( esc_html__( 'WooCommerce Order Barcodes requires %s to be installed and active.', 'woocommerce-order-barcodes' ), '<a href="https://woocommerce.com/" target="_blank">WooCommerce</a>' ) . '</p></div>';
}
</file>

<file path="changelog.txt">
*** WooCommerce Order Barcodes Changelog ***

2025-08-11 - version 1.9.4
* Tweak - WooCommerce 10.1 compatibility.

2025-07-07 - version 1.9.3
* Tweak - WooCommerce 10.0 compatibility.

2025-06-09 - version 1.9.2
* Tweak - WooCommerce 9.9 compatibility.

2025-06-03 - version 1.9.1
* Update Requires headers for WooCommerce compatibility.
* Update to ubuntu-latest to fix QIT tests.

2025-05-05 - version 1.9.0
* Add - 'woocommerce_order_barcode_parameters' filter to allow customization of barcode dimensions, colors, and padding.
* Fix - Barcode scanning issue in dark mode emails by ensuring background extends properly.

2025-04-07 - version 1.8.5
* Tweak - WooCommerce 9.8 compatibility.

2025-03-04 - version 1.8.4
* Tweak - WooCommerce 9.7 compatibility.

2025-01-27 - version 1.8.3
* Tweak - PHP 8.4 compatibility.

2025-01-07 - version 1.8.2
* Fix   - Restore High-Performance Order Storage (HPOS) and WooCommerce Blocks compatibility.

2024-10-29 - version 1.8.1
* Tweak - WordPress 6.7 Compatibility.

2024-09-30 - version 1.8.0
* Add - Background color settings for the barcode to make the barcode scannable in dark theme or page.

2024-07-02 - version 1.7.5
* Tweak - WordPress 6.6 & WooCommerce 9.0 Compatibility.

2024-03-25 - version 1.7.4
* Tweak - WordPress 6.5 compatibility.

2023-12-11 - version 1.7.3
* Add - Declared cart/checkout blocks compatibility.

2023-09-18 - version 1.7.2
* Tweak  - Applying WordPress Coding Standards.

2023-09-05 - version 1.7.1
* Update - Security updates.

2023-08-08 - version 1.7.0
* Add    - New barcode library for PHP 8 compatibility.

2023-06-12 - version 1.6.5
* Update - Security update.

2023-05-01 - version 1.6.4
* Update - Security update.

2023-02-02 - version 1.6.3
* Tweak - To make `display_barcode()` available for other plugins.

2023-01-23 - version 1.6.2
* Fix   - Automatic barcode generation no longer fails with "Barcode will be generated after Order is created!" message.

2023-01-19 - version 1.6.1
* Fix   - Display a notice text on Add New Order screen.

2022-11-03 - version 1.6.0
* Add   - Declared High-Performance Order Storage (HPOS) compatibility.

2022-10-13 - version 1.5.2
* Fix   - Add WCC Box Office compatibility for displaying a ticket barcode.

2022-09-22 - version 1.5.1
* Fix   - Prevent adding order meta box to other post types.
* Tweak - WC 6.9 compatibility.

2022-09-15 - version 1.5.0
* Tweak - Custom Order Tables (COT) compatibility.
* Fix   - Exclude unnecessary files from plugin zip file.

2022-08-15 - version 1.4.0
* Add   - Add bulk generate barcode tools for the order before the plugin being installed.
* Tweak - Transition version numbering to WordPress versioning.
* Tweak - WC 6.7.0 and WP 6.0.1 compatibility.

2022-06-09 - version 1.3.26
* Fix   - Plugin cannot read numerical barcode.

2022-01-05 - version 1.3.25
* Fix   - Missing Languages folder and .pot file in release-ready zip file.

2021-07-27 - version 1.3.24
* Tweak - Remove barcode field in checkout flow, instead create barcode once order created.
* Tweak - Add barcode URL in REST API order creation response.
* Tweak - Display barcode on the edit order page as PNG, instead of HTML. Fixes FireFox display issue.

2020-09-23 - version 1.3.23
* Fix   - Error loading onscan.js file on Barcode Scanner page.

2020-08-19 - version 1.3.22
* Tweak - WordPress 5.5 compatibility.

2020-07-22 - version 1.3.21
* Fix - Show barcode as image in emails and on frontend pages.
* Tweak - Remove email link for viewing barcode in browser.

2020-07-14 - version 1.3.20
* Fix - Add link to order in email.

2020-07-01 - version 1.3.19
* Add - Filter to hide barcode from display. `woocommerce_order_barcodes_display_barcode`
* Fix - Checkout page load performance due to large barcode images.
* Fix - Order barcode scan not displaying invalid notices.
* Tweak - Scan barcode form layout for mobile devices.

2020-06-10 - version 1.3.18
* Tweak - WC 4.2 compatibility.

2020-04-29 - version 1.3.17
* Tweak - WC 4.1 compatibility.

2020-03-04 - version 1.3.16
* Tweak - Remove legacy code.
* Tweak - WC 4.0 compatibility.

2020-01-21 - version 1.3.15
* Add - Extension constant version.

2020-01-13 - version 1.3.14
* Tweak - WC tested up to 3.9

2019-11-04 - version 1.3.13
* Tweak - WC tested up to 3.8

2019-10-22 - version 1.3.12
* Fix - Box Office ticets not displaying.

2019-10-10 - version 1.3.11
* Fix - Barcodes not displaying in order emails.

2019-09-03 - version 1.3.10
* Fix - Store managers could not view barcodes for orders generated by customers.

2019-08-28 - version 1.3.9
* Fix - Barcode visible at URL using order number.

2019-08-20 - version 1.3.8
* Fix - Unable to change barcode types.

2019-08-08 - version 1.3.7
* Tweak - WC tested up to 3.7

2019-04-16 - version 1.3.6
* Tweak - WC tested up to 3.6

2019-03-12 - version 1.3.5
* Fix - Barcode doesn't appear correctly in Gmail.

2018-10-09 - version 1.3.4
* Updated - WC tested up to 3.5

2018-05-23 - version 1.3.3
* Updated - WC tested up to 3.4
* Add - GDPR support

2017-12-13 - version 1.3.2
* Fix - WC 3.3 compatibility.

2017-09-07 - version 1.3.1
* New - Added filter `wc_order_barcodes_remove_image_from_api` to remove the order barcode image from API responses. Disabled by default, in case existing merchants are relying on that image.

2016-01-11 - version 1.3.0
* Tweak - Improved [scan_barcode] form check and notice messages.
* Fix - Invalid action should be warned and order info shouldn't be displayed.

2015-05-15 - version 1.2.1
* Tweak - Minifying CSS
* Fix - Removing Chosen drop down on scan form

2015-03-27 - version 1.2.0
* New - Adding wc_order_barcode() function for fetching a specific order's barcode
* Fix - Fixing PHP strict standards errors

2014-10-03 - version 1.1.2
* Tweak - Adding option to switch off barcode generation (on by default)
* Tweak - Adding 'woocommerce_order_barcodes_barcode_string' filter to allow the generated barode string to be modified on the fly

2014-09-08 - version 1.1.1
* Fix - Fixing CSS for admin barcode display
* Tweak - Updating queries for WooCommerce 2.2

2014-07-14 - version 1.1
* New - Added woocommerce_order_barcodes_do_nonce_check filter that will allow you to disable the nonce check on scanning
* New - Added woocommerce_order_barcodes_scan_permission filter that will allow you to modify the permissions needed to scan barcodes
* Tweak - Added order ID to woocommerce_order_barcodes_complete_order filter
* Tweak - Improved error messages when scanning fails
* Tweak - Improved a few inline comments

2014-06-03 - version 1.0.1
* Adding woocommerce_order_barcodes_complete_order filter to allow third parties to add custom conditions for order completion

2014-06-03 - version 1.0.0
* Initial release! Woo!
</file>

</files>

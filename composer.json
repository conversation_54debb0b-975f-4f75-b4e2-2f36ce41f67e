{"name": "woocommerce/woocommerce-order-barcodes", "description": "Generates unique barcodes for your orders - perfect for e-tickets, packing slips, reservations and a variety of other uses.", "homepage": "http://www.woocommerce.com/products/woocommerce-order-barcodes/", "type": "wordpress-plugin", "license": "GPL-2.0+", "archive": {"exclude": ["!/assets", "!/languages", "!/vendor", "bin", "tests", "node_modules", "Gruntfile.js", "README.md", "package.json", "package-lock.json", "composer.json", "composer.lock", "phpunit.xml.dist", ".*", "woocommerce-order-barcodes.zip", "*.scss", ".phpcs.security.xml", "pnpm-lock.yaml"]}, "require": {"tecnickcom/tc-lib-barcode": "^1.17"}, "require-dev": {"woocommerce/qit-cli": "*", "squizlabs/php_codesniffer": "*", "dealerdirect/phpcodesniffer-composer-installer": "*", "wp-coding-standards/wpcs": "*", "woocommerce/woocommerce-sniffs": "*"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["npm run build && composer install && ./vendor/bin/qit run:security woocommerce-order-barcodes --zip=woocommerce-order-barcodes.zip"]}}
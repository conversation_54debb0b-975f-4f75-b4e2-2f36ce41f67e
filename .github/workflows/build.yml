name: Build
on:
  workflow_call:
    inputs:
      plugin_name:
        required: true
        type: string
        description: 'The name of the plugin (e.g. woocommerce-order-barcodes)'
    outputs:
      plugin_name:
        description: "The name of the plugin"
        value: ${{ jobs.build.outputs.plugin_name }}

jobs:
  build:
    name: Build plugin artifact (zip file)
    runs-on: ubuntu-latest
    outputs:
      plugin_name: ${{ inputs.plugin_name }}
    env:
      PLUGIN_NAME: ${{ inputs.plugin_name }}
    steps:
      - name: Set Git to use HTTPS instead of SSH
        run: git config --global url.https://github.com/.insteadOf git://github.com/

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10

      - uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install PNPM dependencies
        run: pnpm install --frozen-lockfile

      - name: Build plugin zip
        run: pnpm build:qit

      - name: Upload plugin zip
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PLUGIN_NAME }}
          path: ${{ env.PLUGIN_NAME }}.zip

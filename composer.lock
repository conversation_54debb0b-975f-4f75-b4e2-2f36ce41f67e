{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "37d733f3c9b8ff1730ebd60dd829a6ce", "packages": [{"name": "tecnickcom/tc-lib-barcode", "version": "1.18.4", "source": {"type": "git", "url": "https://github.com/tecnickcom/tc-lib-barcode.git", "reference": "cd81392e6e1e57e0f6ff8519b1edbc11d8e47a44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/tc-lib-barcode/zipball/cd81392e6e1e57e0f6ff8519b1edbc11d8e47a44", "reference": "cd81392e6e1e57e0f6ff8519b1edbc11d8e47a44", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-date": "*", "ext-gd": "*", "ext-pcre": "*", "php": ">=5.6", "tecnickcom/tc-lib-color": "^1.14"}, "require-dev": {"pdepend/pdepend": "2.13.0", "phpmd/phpmd": "2.13.0", "phpunit/phpunit": "10.1.2 || 9.6.7 || 8.5.31 || 7.5.20 || 6.5.14 || 5.7.27 || 4.8.36", "squizlabs/php_codesniffer": "3.7.2 || 2.9.2"}, "type": "library", "autoload": {"psr-4": {"Com\\Tecnick\\Barcode\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "PHP library to generate linear and bidimensional barcodes", "homepage": "http://www.tecnick.com", "keywords": ["3 of 9", "ANSI MH10.8M-1983", "CBC", "CODABAR", "CODE 11", "CODE 128 A B C", "CODE 39", "CODE 93", "EAN 13", "EAN 8", "ECC200", "ISO IEC 15438 2006", "ISO IEC 16022", "ISO IEC 24778 2008", "Intelligent Mail Barcode", "Interleaved 2 of 5", "KIX", "<PERSON><PERSON>", "MSI", "Onecode", "PHARMACODE", "PHARMACODE TWO-TRACKS", "POSTNET", "RMS4CC", "Standard 2 of 5", "UPC-A", "UPC-E", "USD-3", "USPS-B-3200", "USS-93", "aztec", "barcode", "datamatrix", "pdf417", "planet", "qr-code", "royal mail", "tc-lib-barcode", "upc"], "support": {"issues": "https://github.com/tecnickcom/tc-lib-barcode/issues", "source": "https://github.com/tecnickcom/tc-lib-barcode/tree/1.18.4"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_donations&currency_code=GBP&business=<EMAIL>&item_name=donation%20for%20tc-lib-barcode%20project", "type": "custom"}], "time": "2023-10-23T09:30:01+00:00"}, {"name": "tecnickcom/tc-lib-color", "version": "1.14.39", "source": {"type": "git", "url": "https://github.com/tecnickcom/tc-lib-color.git", "reference": "f7a414e7ddbdcd98105506ca1eecc68d4820fb89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/tc-lib-color/zipball/f7a414e7ddbdcd98105506ca1eecc68d4820fb89", "reference": "f7a414e7ddbdcd98105506ca1eecc68d4820fb89", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3"}, "require-dev": {"pdepend/pdepend": "2.13.0", "phpmd/phpmd": "2.13.0", "phpunit/phpunit": "10.1.2 || 9.6.7 || 8.5.31 || 7.5.20 || 6.5.14 || 5.7.27 || 4.8.36", "squizlabs/php_codesniffer": "3.7.2 || 2.9.2"}, "type": "library", "autoload": {"psr-4": {"Com\\Tecnick\\Color\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "PHP library to manipulate various color representations", "homepage": "http://www.tecnick.com", "keywords": ["cmyk", "color", "colors", "colour", "colours", "hsl", "hsla", "javascript", "rgb", "rgba", "tc-lib-color", "web"], "support": {"issues": "https://github.com/tecnickcom/tc-lib-color/issues", "source": "https://github.com/tecnickcom/tc-lib-color/tree/1.14.39"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_donations&currency_code=GBP&business=<EMAIL>&item_name=donation%20for%20tc-lib-color%20project", "type": "custom"}], "time": "2023-10-23T09:28:20+00:00"}], "packages-dev": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/composer-installer.git", "reference": "e9cf5e4bbf7eeaf9ef5db34938942602838fc2b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/composer-installer/zipball/e9cf5e4bbf7eeaf9ef5db34938942602838fc2b1", "reference": "e9cf5e4bbf7eeaf9ef5db34938942602838fc2b1", "shasum": ""}, "require": {"composer-plugin-api": "^2.2", "php": ">=5.4", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "^2.2", "ext-json": "*", "ext-zip": "*", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpcompatibility/php-compatibility": "^9.0", "yoast/phpunit-polyfills": "^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://frenck.dev", "role": "Open source developer"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "security": "https://github.com/PHPCSStandards/composer-installer/security/policy", "source": "https://github.com/PHPCSStandards/composer-installer"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-07-17T20:45:56+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "db9508f7b1474469d9d3c53b86f817e344732678"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/db9508f7b1474469d9d3c53b86f817e344732678", "reference": "db9508f7b1474469d9d3c53b86f817e344732678", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-deprecation-rules": "^2.0.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.3.0"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2025-08-14T07:29:31+00:00"}, {"name": "jetbrains/phpstorm-stubs", "version": "v2024.3", "source": {"type": "git", "url": "https://github.com/JetBrains/phpstorm-stubs.git", "reference": "0e82bdfe850c71857ee4ee3501ed82a9fc5d043c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JetBrains/phpstorm-stubs/zipball/0e82bdfe850c71857ee4ee3501ed82a9fc5d043c", "reference": "0e82bdfe850c71857ee4ee3501ed82a9fc5d043c", "shasum": ""}, "require-dev": {"friendsofphp/php-cs-fixer": "v3.64.0", "nikic/php-parser": "v5.3.1", "phpdocumentor/reflection-docblock": "5.6.0", "phpunit/phpunit": "11.4.3"}, "type": "library", "autoload": {"files": ["PhpStormStubsMap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "PHP runtime & extensions header files for PhpStorm", "homepage": "https://www.jetbrains.com/phpstorm", "keywords": ["autocomplete", "code", "inference", "inspection", "jetbrains", "phpstorm", "stubs", "type"], "support": {"source": "https://github.com/JetBrains/phpstorm-stubs/tree/v2024.3"}, "time": "2024-12-14T08:03:12+00:00"}, {"name": "lucasbustamante/stubz", "version": "0.1", "source": {"type": "git", "url": "https://github.com/Luc45/stubz.git", "reference": "ee6c870c201f1d168eb9c09d0428e14f654078ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Luc45/stubz/zipball/ee6c870c201f1d168eb9c09d0428e14f654078ae", "reference": "ee6c870c201f1d168eb9c09d0428e14f654078ae", "shasum": ""}, "require": {"fidry/cpu-core-counter": "^1.2", "php": "^8.2", "roave/better-reflection": "^6.0", "symfony/finder": "^6.0"}, "suggest": {"ext-pcntl": "For running in parallel.", "ext-posix": "For running in parallel."}, "bin": ["bin/stubz"], "type": "library", "autoload": {"psr-4": {"Stubz\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A command-line PHP stub generator using BetterReflection.", "support": {"issues": "https://github.com/Luc45/stubz/issues", "source": "https://github.com/Luc45/stubz/tree/0.1"}, "time": "2025-03-01T02:23:00+00:00"}, {"name": "nikic/php-parser", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "f103601b29efebd7ff4a1ca7b3eeea9e3336a2a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/f103601b29efebd7ff4a1ca7b3eeea9e3336a2a2", "reference": "f103601b29efebd7ff4a1ca7b3eeea9e3336a2a2", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.6.1"}, "time": "2025-08-13T20:13:15+00:00"}, {"name": "php-stubs/wordpress-stubs", "version": "v6.8.2", "source": {"type": "git", "url": "https://github.com/php-stubs/wordpress-stubs.git", "reference": "9c8e22e437463197c1ec0d5eaa9ddd4a0eb6d7f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-stubs/wordpress-stubs/zipball/9c8e22e437463197c1ec0d5eaa9ddd4a0eb6d7f8", "reference": "9c8e22e437463197c1ec0d5eaa9ddd4a0eb6d7f8", "shasum": ""}, "conflict": {"phpdocumentor/reflection-docblock": "5.6.1"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "nikic/php-parser": "^5.5", "php": "^7.4 || ^8.0", "php-stubs/generator": "^0.8.3", "phpdocumentor/reflection-docblock": "^5.4.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^9.5", "szepeviktor/phpcs-psr-12-neutron-hybrid-ruleset": "^1.1.1", "wp-coding-standards/wpcs": "3.1.0 as 2.3.0"}, "suggest": {"paragonie/sodium_compat": "Pure PHP implementation of libsodium", "symfony/polyfill-php80": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "szepeviktor/phpstan-wordpress": "WordPress extensions for PHPStan"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WordPress function and class declaration stubs for static analysis.", "homepage": "https://github.com/php-stubs/wordpress-stubs", "keywords": ["PHPStan", "static analysis", "wordpress"], "support": {"issues": "https://github.com/php-stubs/wordpress-stubs/issues", "source": "https://github.com/php-stubs/wordpress-stubs/tree/v6.8.2"}, "time": "2025-07-16T06:41:00+00:00"}, {"name": "php-stubs/wp-cli-stubs", "version": "v2.12.0", "source": {"type": "git", "url": "https://github.com/php-stubs/wp-cli-stubs.git", "reference": "af16401e299a3fd2229bd0fa9a037638a4174a9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-stubs/wp-cli-stubs/zipball/af16401e299a3fd2229bd0fa9a037638a4174a9d", "reference": "af16401e299a3fd2229bd0fa9a037638a4174a9d", "shasum": ""}, "require": {"php-stubs/wordpress-stubs": "^4.7 || ^5.0 || ^6.0"}, "require-dev": {"php": "~7.3 || ~8.0", "php-stubs/generator": "^0.8.0"}, "suggest": {"symfony/polyfill-php73": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "szepeviktor/phpstan-wordpress": "WordPress extensions for PHPStan"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI function and class declaration stubs for static analysis.", "homepage": "https://github.com/php-stubs/wp-cli-stubs", "keywords": ["PHPStan", "static analysis", "wordpress", "wp-cli"], "support": {"issues": "https://github.com/php-stubs/wp-cli-stubs/issues", "source": "https://github.com/php-stubs/wp-cli-stubs/tree/v2.12.0"}, "time": "2025-06-10T09:58:05+00:00"}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "time": "2019-12-27T09:44:58+00:00"}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.3", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git", "reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}, "funding": [{"url": "https://github.com/PHPCompatibility", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-04-24T21:30:46+00:00"}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "2.1.7", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git", "reference": "5bfbbfbabb3df2b9a83e601de9153e4a7111962c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/5bfbbfbabb3df2b9a83e601de9153e4a7111962c", "reference": "5bfbbfbabb3df2b9a83e601de9153e4a7111962c", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0", "squizlabs/php_codesniffer": "^3.3"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityWP/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}, "funding": [{"url": "https://github.com/PHPCompatibility", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcompatibility", "type": "thanks_dev"}], "time": "2025-05-12T16:38:37+00:00"}, {"name": "phpcsstandards/phpcsextra", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSExtra.git", "reference": "fa4b8d051e278072928e32d817456a7fdb57b6ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSExtra/zipball/fa4b8d051e278072928e32d817456a7fdb57b6ca", "reference": "fa4b8d051e278072928e32d817456a7fdb57b6ca", "shasum": ""}, "require": {"php": ">=5.4", "phpcsstandards/phpcsutils": "^1.1.0", "squizlabs/php_codesniffer": "^3.13.0 || ^4.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpcsstandards/phpcsdevcs": "^1.1.6", "phpcsstandards/phpcsdevtools": "^1.2.1", "phpunit/phpunit": "^4.5 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSExtra/graphs/contributors"}], "description": "A collection of sniffs and standards for use with PHP_CodeSniffer.", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHPCSExtra/issues", "security": "https://github.com/PHPCSStandards/PHPCSExtra/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSExtra"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-06-14T07:40:39+00:00"}, {"name": "phpcsstandards/phpcsutils", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSUtils.git", "reference": "f7eb16f2fa4237d5db9e8fed8050239bee17a9bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSUtils/zipball/f7eb16f2fa4237d5db9e8fed8050239bee17a9bd", "reference": "f7eb16f2fa4237d5db9e8fed8050239bee17a9bd", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^3.13.0 || ^4.0"}, "require-dev": {"ext-filter": "*", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpcsstandards/phpcsdevcs": "^1.1.6", "yoast/phpunit-polyfills": "^1.1.0 || ^2.0.0 || ^3.0.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "autoload": {"classmap": ["PHPCSUtils/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "description": "A suite of utility functions for use with PHP_CodeSniffer", "homepage": "https://phpcsutils.com/", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "phpcs3", "phpcs4", "standards", "static analysis", "tokens", "utility"], "support": {"docs": "https://phpcsutils.com/", "issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "security": "https://github.com/PHPCSStandards/PHPCSUtils/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSUtils"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-08-10T01:04:45+00:00"}, {"name": "phpstan/phpstan", "version": "2.1.22", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "41600c8379eb5aee63e9413fe9e97273e25d57e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/41600c8379eb5aee63e9413fe9e97273e25d57e4", "reference": "41600c8379eb5aee63e9413fe9e97273e25d57e4", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-08-04T19:17:37+00:00"}, {"name": "roave/better-reflection", "version": "6.59.0", "source": {"type": "git", "url": "https://github.com/Roave/BetterReflection.git", "reference": "e59267bf734297e1abfc4f7ddb3d35e4be95c0f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Roave/BetterReflection/zipball/e59267bf734297e1abfc4f7ddb3d35e4be95c0f6", "reference": "e59267bf734297e1abfc4f7ddb3d35e4be95c0f6", "shasum": ""}, "require": {"ext-json": "*", "jetbrains/phpstorm-stubs": "2024.3", "nikic/php-parser": "^5.4.0", "php": "~8.2.0 || ~8.3.2 || ~8.4.1"}, "conflict": {"thecodingmachine/safe": "<1.1.3"}, "require-dev": {"phpbench/phpbench": "^1.4.1", "phpunit/phpunit": "^11.5.21"}, "suggest": {"composer/composer": "Required to use the ComposerSourceLocator"}, "type": "library", "autoload": {"psr-4": {"Roave\\BetterReflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/asgrim"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/geeh"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/kukulich"}], "description": "Better Reflection - an improved code reflection API", "support": {"issues": "https://github.com/Roave/BetterReflection/issues", "source": "https://github.com/Roave/BetterReflection/tree/6.59.0"}, "time": "2025-05-27T21:09:39+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.13.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "5b5e3821314f947dd040c70f7992a64eac89025c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5b5e3821314f947dd040c70f7992a64eac89025c", "reference": "5b5e3821314f947dd040c70f7992a64eac89025c", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-06-17T22:17:01+00:00"}, {"name": "symfony/finder", "version": "v6.4.24", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "73089124388c8510efb8d2d1689285d285937b08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/73089124388c8510efb8d2d1689285d285937b08", "reference": "73089124388c8510efb8d2d1689285d285937b08", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.24"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-15T12:02:45+00:00"}, {"name": "szepeviktor/phpstan-wordpress", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/szepeviktor/phpstan-wordpress.git", "reference": "963887b04c21fe7ac78e61c1351f8b00fff9f8f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/szepeviktor/phpstan-wordpress/zipball/963887b04c21fe7ac78e61c1351f8b00fff9f8f8", "reference": "963887b04c21fe7ac78e61c1351f8b00fff9f8f8", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "php-stubs/wordpress-stubs": "^6.6.2", "phpstan/phpstan": "^2.0"}, "require-dev": {"composer/composer": "^2.1.14", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.1", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.0", "szepeviktor/phpcs-psr-12-neutron-hybrid-ruleset": "^1.0", "wp-coding-standards/wpcs": "3.1.0 as 2.3.0"}, "suggest": {"swissspidy/phpstan-no-private": "Detect usage of internal core functions, classes and methods"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"SzepeViktor\\PHPStan\\WordPress\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WordPress extensions for PHPStan", "keywords": ["PHPStan", "code analyse", "code analysis", "static analysis", "wordpress"], "support": {"issues": "https://github.com/szepeviktor/phpstan-wordpress/issues", "source": "https://github.com/szepeviktor/phpstan-wordpress/tree/v2.0.2"}, "time": "2025-02-12T18:43:37+00:00"}, {"name": "woocommerce/qit-cli", "version": "0.10.0", "source": {"type": "git", "url": "https://github.com/woocommerce/qit-cli.git", "reference": "42c4722bb71940dc0435103775439588e923e1cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/qit-cli/zipball/42c4722bb71940dc0435103775439588e923e1cd", "reference": "42c4722bb71940dc0435103775439588e923e1cd", "shasum": ""}, "require": {"ext-curl": "*", "php": "^7.2.5 | ^8"}, "bin": ["qit"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "description": "A command line interface for WooCommerce Quality Insights Toolkit (QIT).", "support": {"issues": "https://github.com/woocommerce/qit-cli/issues", "source": "https://github.com/woocommerce/qit-cli/tree/0.10.0"}, "time": "2025-05-20T15:58:42+00:00"}, {"name": "woocommerce/woocommerce-sniffs", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/woocommerce/woocommerce-sniffs.git", "reference": "3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/woocommerce-sniffs/zipball/3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8", "reference": "3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0.0", "php": ">=7.0", "phpcompatibility/phpcompatibility-wp": "^2.1.0", "wp-coding-standards/wpcs": "^3.0.0"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WooCommerce sniffs", "keywords": ["phpcs", "standards", "static analysis", "woocommerce", "wordpress"], "support": {"issues": "https://github.com/woocommerce/woocommerce-sniffs/issues", "source": "https://github.com/woocommerce/woocommerce-sniffs/tree/1.0.0"}, "time": "2023-09-29T13:52:33+00:00"}, {"name": "wp-coding-standards/wpcs", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "d2421de7cec3274ae622c22c744de9a62c7925af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/d2421de7cec3274ae622c22c744de9a62c7925af", "reference": "d2421de7cec3274ae622c22c744de9a62c7925af", "shasum": ""}, "require": {"ext-filter": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-xmlreader": "*", "php": ">=5.4", "phpcsstandards/phpcsextra": "^1.4.0", "phpcsstandards/phpcsutils": "^1.1.0", "squizlabs/php_codesniffer": "^3.13.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "suggest": {"ext-iconv": "For improved results", "ext-mbstring": "For improved results"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "funding": [{"url": "https://opencollective.com/php_codesniffer", "type": "custom"}], "time": "2025-07-24T20:08:31+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}